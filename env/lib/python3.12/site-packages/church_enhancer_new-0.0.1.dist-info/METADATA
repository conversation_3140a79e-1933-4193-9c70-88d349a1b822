Metadata-Version: 2.4
Name: church_enhancer_new
Version: 0.0.1
Summary: A system for managing SDA church financial records, member contributions, and department budgets.
Author-email: innocent <PERSON><PERSON> <<EMAIL>>
Requires-Python: >=3.10
Description-Content-Type: text/markdown

### Church Enhancer

A system for managing SDA church financial records, member contributions, and department budgets.

### Installation

You can install this app using the [bench](https://github.com/frappe/bench) CLI:

```bash
cd $PATH_TO_YOUR_BENCH
bench get-app $URL_OF_THIS_REPO --branch develop
bench install-app church_enhancer_new
```

### Contributing

This app uses `pre-commit` for code formatting and linting. Please [install pre-commit](https://pre-commit.com/#installation) and enable it for this repository:

```bash
cd apps/church_enhancer_new
pre-commit install
```

Pre-commit is configured to use the following tools for checking and formatting your code:

- ruff
- eslint
- prettier
- pyupgrade

### CI

This app can use GitHub Actions for CI. The following workflows are configured:

- CI: Installs this app and runs unit tests on every push to `develop` branch.
- Linters: Runs [Frappe Semgrep Rules](https://github.com/frappe/semgrep-rules) and [pip-audit](https://pypi.org/project/pip-audit/) on every pull request.


### License

mit

