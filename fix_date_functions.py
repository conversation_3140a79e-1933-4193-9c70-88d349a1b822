#!/usr/bin/env python3

import os
import re
import sys

def fix_date_functions_in_file(file_path):
    """Fix Year and Month function imports in a Python file"""
    
    print(f"Processing: {file_path}")
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    original_content = content
    
    # Check if file uses Year or Month functions
    uses_year = 'Year(' in content
    uses_month = 'Month(' in content
    
    if not (uses_year or uses_month):
        print(f"  - No Year/Month functions found")
        return False
    
    # Check if file already has the custom function definitions
    if 'def Year(field):' in content and 'def Month(field):' in content:
        print(f"  - Already has custom Year/Month functions")
        return False
    
    # Remove Year, Month from imports if present
    content = re.sub(r', Year, Month', '', content)
    content = re.sub(r', Year', '', content)
    content = re.sub(r', Month', '', content)
    content = re.sub(r'Year, Month, ', '', content)
    content = re.sub(r'Year, ', '', content)
    content = re.sub(r'Month, ', '', content)
    
    # Add CustomFunction import if not present
    if 'from frappe.query_builder.terms import CustomFunction' not in content:
        # Find the line with frappe.query_builder.functions import
        functions_import_pattern = r'(from frappe\.query_builder\.functions import [^\n]+)\n'
        match = re.search(functions_import_pattern, content)
        
        if match:
            # Add CustomFunction import after the functions import
            content = content.replace(
                match.group(1),
                match.group(1) + '\nfrom frappe.query_builder.terms import CustomFunction'
            )
    
    # Add custom function definitions
    custom_functions = '''
# Define custom functions for date operations
def Year(field):
	return CustomFunction("YEAR", [field])

def Month(field):
	return CustomFunction("MONTH", [field])
'''
    
    # Find where to insert the custom functions (after imports, before first function)
    # Look for the first function definition
    first_function_pattern = r'\n\ndef [a-zA-Z_]'
    match = re.search(first_function_pattern, content)
    
    if match:
        insert_pos = match.start() + 1  # After the newline
        content = content[:insert_pos] + custom_functions + content[insert_pos:]
    else:
        # If no function found, add at the end of imports
        imports_end_pattern = r'(from frappe\.utils import [^\n]+\n)'
        match = re.search(imports_end_pattern, content)
        if match:
            content = content.replace(match.group(1), match.group(1) + custom_functions)
    
    # Write back if changes were made
    if content != original_content:
        with open(file_path, 'w') as f:
            f.write(content)
        print(f"  - Added custom Year/Month functions")
        return True
    else:
        print(f"  - No changes needed")
        return False

def main():
    """Fix date functions in all report files"""
    
    # Base directory for reports
    base_dir = "apps/stewardpro/stewardpro/church_management/report"
    
    if not os.path.exists(base_dir):
        print(f"Error: Directory {base_dir} not found")
        sys.exit(1)
    
    # Find all Python files in report directories
    python_files = []
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            if file.endswith('.py') and not file.startswith('__'):
                python_files.append(os.path.join(root, file))
    
    print(f"Found {len(python_files)} Python files to process")
    print("=" * 50)
    
    updated_files = 0
    for file_path in python_files:
        if fix_date_functions_in_file(file_path):
            updated_files += 1
        print()
    
    print("=" * 50)
    print(f"Processing complete. Updated {updated_files} files.")

if __name__ == "__main__":
    main()
