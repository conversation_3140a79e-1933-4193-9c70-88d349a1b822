#!/usr/bin/env python3

import sys
import os

# Add the apps directory to Python path
sys.path.insert(0, '/home/<USER>/dev/velocitec/apps')

def test_report_imports():
    """Test if all reports can be imported without errors"""
    
    reports_to_test = [
        "stewardpro.church_management.report.financial_summary.financial_summary",
        "stewardpro.church_management.report.tithes_and_offerings_report.tithes_and_offerings_report",
        "stewardpro.church_management.report.remittance_report.remittance_report",
        "stewardpro.church_management.report.departmental_budget_report.departmental_budget_report",
        "stewardpro.church_management.report.expense_report.expense_report",
        "stewardpro.church_management.report.camp_meeting_contributions_report.camp_meeting_contributions_report",
        "stewardpro.church_management.report.church_building_fund_report.church_building_fund_report",
        "stewardpro.church_management.report.pending_remittance_report.pending_remittance_report",
        "stewardpro.church_management.report.annual_conference_report.annual_conference_report"
    ]
    
    print("Testing Report Imports...")
    print("=" * 50)
    
    success_count = 0
    for report_module in reports_to_test:
        try:
            # Try to import the module
            module = __import__(report_module, fromlist=['execute'])
            
            # Check if execute function exists
            if hasattr(module, 'execute'):
                print(f"✅ {report_module.split('.')[-1]}: Import successful")
                success_count += 1
            else:
                print(f"❌ {report_module.split('.')[-1]}: No execute function")
                
        except ImportError as e:
            print(f"❌ {report_module.split('.')[-1]}: Import error - {e}")
        except Exception as e:
            print(f"❌ {report_module.split('.')[-1]}: Error - {e}")
    
    print("=" * 50)
    print(f"Results: {success_count}/{len(reports_to_test)} reports imported successfully")
    
    return success_count == len(reports_to_test)

def test_qb_syntax():
    """Test QB syntax by checking for common issues"""
    
    print("\nTesting QB Syntax...")
    print("=" * 50)
    
    # Check for remaining frappe.qb.functions calls
    import subprocess
    
    try:
        result = subprocess.run([
            'grep', '-r', 'frappe\.qb\.functions\.', 
            'apps/stewardpro/stewardpro/church_management/report'
        ], capture_output=True, text=True, cwd='/home/<USER>/dev/velocitec')
        
        if result.returncode == 0 and result.stdout.strip():
            print("❌ Found remaining frappe.qb.functions calls:")
            print(result.stdout)
            return False
        else:
            print("✅ No remaining frappe.qb.functions calls found")
            return True
            
    except Exception as e:
        print(f"❌ Error checking QB syntax: {e}")
        return False

if __name__ == "__main__":
    print("Church Management Reports - QB Fix Verification")
    print("=" * 60)
    
    # Test imports
    imports_ok = test_report_imports()
    
    # Test QB syntax
    syntax_ok = test_qb_syntax()
    
    print("\n" + "=" * 60)
    if imports_ok and syntax_ok:
        print("🎉 All tests passed! Reports should work correctly now.")
        sys.exit(0)
    else:
        print("⚠️  Some issues found. Check the output above.")
        sys.exit(1)
