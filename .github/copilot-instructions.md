# Copilot Instructions for velocitec-erpnext

## Project Overview
This is a multi-app Frappe/ERPNext monorepo for business management, messaging, and payments. Key apps include:
- `erpnext`: Core ERP features (accounting, HR, manufacturing, etc.)
- `frappe`: Underlying framework (Python, JS, MariaDB/Postgres, Redis)
- `raven`: Messaging platform with React/React Native frontends, deep Frappe integration
- `payments`: Payment gateway integrations and overrides
- `velocetec`: Custom business logic and extensions

## Architecture & Patterns
- **Apps are in `apps/`**. Each app has its own `README.md`, `pyproject.toml`, and submodules.
- **Custom fields, fixtures, and patches** are managed via JSON/YAML in `apps/<app>/<app>/patches/` and similar folders.
- **Frontend (React/Vite) and Mobile (React Native/Expo)** code for Raven is in `apps/raven/frontend` and `apps/raven/mobile`.
- **Overrides**: Custom logic often lives in `overrides/` (see payments) and is loaded via hooks.
- **Utils**: Shared utilities are in `utils/` and imported via `__init__.py` for namespacing.

## Developer Workflows
- **Install/Setup**: Use Frappe Bench. Example:
  - `bench get-app <app>`
  - `bench --site <site> install-app <app>`
- **Local Dev**: Enable developer mode (`bench set-config -g developer_mode 1`).
- **Frontend Dev (Raven)**: `yarn run dev` in `apps/raven/frontend` (see `.env.local` for config).
- **Mobile Dev (Raven)**: Use Expo CLI (`yarn run ios`/`yarn run android` in `apps/raven/mobile`).
- **Testing**: Standard Frappe/ERPNext test runners. No custom test harnesses detected.
- **Patches/Fixtures**: Custom fields and DocTypes are exported as JSON/YAML fixtures for migration.

## Conventions & Integration
- **Naming**: Use Frappe/ERPNext conventions for DocTypes, fields, and modules.
- **Permissions**: Role-based permissions are set in DocType JSON and via Frappe's standard mechanisms.
- **External Services**: Payments app integrates with gateways (Razorpay, Stripe, etc.) via DocType and template overrides.
- **Messaging**: Raven uses socket.io, React SDK, and Frappe hooks for real-time features.
- **License**: Most apps are MIT or AGPLv3. See individual `license.txt` files.

## Key Files & Directories
- `apps/<app>/README.md`: App-specific docs and setup
- `apps/<app>/<app>/patches/`: Custom fields, fixtures, and migration scripts
- `apps/<app>/overrides/`: Frappe/ERPNext controller overrides
- `apps/raven/frontend/`, `apps/raven/mobile/`: Frontend/mobile code
- `Procfile`, `pyproject.toml`, `package.json`: App/service configuration

## Example Patterns
- To add a custom field: create a JSON in `patches/custom_fields_json/` and export as fixture
- To override a controller: place Python in `overrides/` and reference in `hooks.py`
- To run frontend dev server: `cd apps/raven/frontend && yarn run dev`

## Tips for AI Agents
- Always follow Frappe/ERPNext naming and export conventions
- Use hooks and fixtures for migrations and customizations
- Reference app-specific `README.md` for setup and integration details
- For messaging/payment integrations, check `overrides/` and `templates/`

---

If any section is unclear or missing, please provide feedback to improve these instructions.
