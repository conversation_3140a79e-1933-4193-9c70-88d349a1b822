2025-08-10 08:28:24,646 WARNING database DDL Query made to DB:
create table `tabAssignment Rule User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-10 08:28:24,735 WARNING database DDL Query made to DB:
create table `tabAuto Repeat Day` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`day` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-10 08:28:24,847 WARNING database DDL Query made to DB:
create table `tabMilestone` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_type` varchar(140),
`reference_name` varchar(140),
`track_field` varchar(140),
`value` varchar(140),
`milestone_tracker` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_type`(`reference_type`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-10 08:28:24,903 WARNING database DDL Query made to DB:
ALTER TABLE `tabMilestone`
				ADD INDEX IF NOT EXISTS `reference_type_reference_name_index`(reference_type, reference_name)
2025-08-10 09:18:24,218 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-10 09:18:24,678 WARNING database DDL Query made to DB:
create table `tabChurch Expense` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`expense_date` date,
`department` varchar(140),
`budget_reference` varchar(140),
`expense_category` varchar(140),
`expense_description` varchar(140),
`amount` decimal(21,9) not null default 0,
`payment_mode` varchar(140),
`vendor_supplier` varchar(140),
`invoice_number` varchar(140),
`receipt_number` varchar(140),
`reference_number` varchar(140),
`approved_by` varchar(140),
`approval_date` date,
`status` varchar(140) default 'Draft',
`attachments` text,
`notes` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-10 09:18:24,888 WARNING database DDL Query made to DB:
create table `tabDepartment Budget Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_name` varchar(140),
`category` varchar(140),
`budgeted_amount` decimal(21,9) not null default 0,
`spent_amount` decimal(21,9) not null default 0,
`remaining_amount` decimal(21,9) not null default 0,
`description` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-10 09:18:24,961 WARNING database DDL Query made to DB:
create table `tabChurch Member` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`member_id` varchar(140) unique,
`full_name` varchar(140),
`status` varchar(140) default 'Active',
`role` varchar(140) default 'Member',
`date_of_birth` date,
`gender` varchar(140),
`baptism_date` date,
`membership_date` date,
`contact` varchar(140),
`email` varchar(140),
`address` text,
`city` varchar(140),
`emergency_contact_name` varchar(140),
`emergency_contact_phone` varchar(140),
`relationship` varchar(140),
`occupation` varchar(140),
`employer` varchar(140),
`skills_talents` text,
`notes` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-10 09:18:25,021 WARNING database DDL Query made to DB:
create table `tabRemittance Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_type` varchar(140),
`description` varchar(140),
`amount` decimal(21,9) not null default 0,
`percentage` decimal(21,9) not null default 0,
`period_from` date,
`period_to` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-10 09:18:25,087 WARNING database DDL Query made to DB:
create table `tabRemittance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`remittance_date` date,
`remittance_period` varchar(140) default 'Monthly',
`status` varchar(140) default 'Draft',
`organization_type` varchar(140),
`organization_name` varchar(140),
`contact_person` varchar(140),
`contact_details` text,
`tithe_amount` decimal(21,9) not null default 0,
`offering_to_field_amount` decimal(21,9) not null default 0,
`special_offerings_amount` decimal(21,9) not null default 0,
`other_remittances_amount` decimal(21,9) not null default 0,
`total_remittance_amount` decimal(21,9) not null default 0,
`payment_mode` varchar(140),
`reference_number` varchar(140),
`prepared_by` varchar(140),
`preparation_date` date,
`approved_by` varchar(140),
`approval_date` date,
`notes` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-10 09:18:25,163 WARNING database DDL Query made to DB:
create table `tabDepartment Budget` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`department` varchar(140),
`fiscal_year` varchar(140),
`status` varchar(140) default 'Draft',
`budget_period` varchar(140) default 'Annual',
`total_budget_amount` decimal(21,9) not null default 0,
`allocated_amount` decimal(21,9) not null default 0,
`spent_amount` decimal(21,9) not null default 0,
`remaining_amount` decimal(21,9) not null default 0,
`description` text,
`notes` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-10 09:18:25,244 WARNING database DDL Query made to DB:
create table `tabTithes and Offerings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`member` varchar(140),
`date` date,
`payment_mode` varchar(140) default 'Cash',
`tithe_amount` decimal(21,9) not null default 0,
`offering_amount` decimal(21,9) not null default 0,
`offering_to_field` decimal(21,9) not null default 0,
`offering_to_church` decimal(21,9) not null default 0,
`campmeeting_offering` decimal(21,9) not null default 0,
`church_building_offering` decimal(21,9) not null default 0,
`total_amount` decimal(21,9) not null default 0,
`receipt_number` varchar(140),
`notes` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-10 09:44:32,566 WARNING database DDL Query made to DB:
create table `tabFiscal Year` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-10 09:45:02,176 WARNING database DDL Query made to DB:
ALTER TABLE `tabFiscal Year` ADD COLUMN `fiscal_year` varchar(140)
2025-08-10 09:55:13,474 WARNING database DDL Query made to DB:
ALTER TABLE `tabFiscal Year` ADD UNIQUE INDEX IF NOT EXISTS fiscal_year (`fiscal_year`)
2025-08-10 10:05:39,836 WARNING database DDL Query made to DB:
ALTER TABLE `tabFiscal Year` ADD COLUMN `is_active` int(1) not null default 0
2025-08-10 10:13:29,592 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepartment Budget` ADD COLUMN `amended_from` varchar(140)
2025-08-10 10:13:29,607 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepartment Budget` MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `total_budget_amount` decimal(21,9) not null default 0, MODIFY `spent_amount` decimal(21,9) not null default 0, MODIFY `remaining_amount` decimal(21,9) not null default 0
2025-08-10 10:13:29,633 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepartment Budget` ADD INDEX `amended_from_index`(`amended_from`)
2025-08-10 10:13:59,173 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepartment Budget` MODIFY `total_budget_amount` decimal(21,9) not null default 0, MODIFY `spent_amount` decimal(21,9) not null default 0, MODIFY `remaining_amount` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0
2025-08-10 10:15:23,948 WARNING database DDL Query made to DB:
alter table `tabDepartment Budget Item` add column if not exists parent varchar(140)
2025-08-10 10:15:23,948 WARNING database DDL Query made to DB:
alter table `tabDepartment Budget Item` add column if not exists parenttype varchar(140)
2025-08-10 10:15:23,949 WARNING database DDL Query made to DB:
alter table `tabDepartment Budget Item` add column if not exists parentfield varchar(140)
2025-08-10 10:15:24,037 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepartment Budget Item` MODIFY `budgeted_amount` decimal(21,9) not null default 0, MODIFY `spent_amount` decimal(21,9) not null default 0, MODIFY `remaining_amount` decimal(21,9) not null default 0
2025-08-10 11:05:35,337 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-10 11:05:35,803 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepartment Budget Item` MODIFY `budgeted_amount` decimal(21,9) not null default 0, MODIFY `spent_amount` decimal(21,9) not null default 0, MODIFY `remaining_amount` decimal(21,9) not null default 0
2025-08-10 11:05:36,129 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepartment Budget` MODIFY `spent_amount` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `remaining_amount` decimal(21,9) not null default 0, MODIFY `total_budget_amount` decimal(21,9) not null default 0
2025-08-10 14:19:21,307 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-11 15:37:52,247 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-11 15:39:50,750 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-11 16:22:37,947 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-11 16:41:25,265 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
