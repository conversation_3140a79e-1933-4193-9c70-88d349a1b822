2025-07-04 17:32:30,022 WARNING database DDL Query made to DB:
create table `tabShipment Delivery Note` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`delivery_note` varchar(140),
`grand_total` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:30,368 WARNING database DDL Query made to DB:
create table `tabDelivery Note Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`barcode` varchar(140),
`has_item_scanned` int(1) not null default 0,
`item_code` varchar(140),
`item_name` varchar(140),
`customer_item_code` varchar(140),
`description` longtext,
`brand` varchar(140),
`item_group` varchar(140),
`image` text,
`qty` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`stock_qty` decimal(21,9) not null default 0,
`returned_qty` decimal(21,9) not null default 0,
`price_list_rate` decimal(21,9) not null default 0,
`base_price_list_rate` decimal(21,9) not null default 0,
`margin_type` varchar(140),
`margin_rate_or_amount` decimal(21,9) not null default 0,
`rate_with_margin` decimal(21,9) not null default 0,
`discount_percentage` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`distributed_discount_amount` decimal(21,9) not null default 0,
`base_rate_with_margin` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`base_rate` decimal(21,9) not null default 0,
`base_amount` decimal(21,9) not null default 0,
`pricing_rules` text,
`stock_uom_rate` decimal(21,9) not null default 0,
`is_free_item` int(1) not null default 0,
`grant_commission` int(1) not null default 0,
`net_rate` decimal(21,9) not null default 0,
`net_amount` decimal(21,9) not null default 0,
`item_tax_template` varchar(140),
`base_net_rate` decimal(21,9) not null default 0,
`base_net_amount` decimal(21,9) not null default 0,
`billed_amt` decimal(21,9) not null default 0,
`incoming_rate` decimal(21,9) not null default 0,
`weight_per_unit` decimal(21,9) not null default 0,
`total_weight` decimal(21,9) not null default 0,
`weight_uom` varchar(140),
`warehouse` varchar(140),
`target_warehouse` varchar(140),
`quality_inspection` varchar(140),
`allow_zero_valuation_rate` int(1) not null default 0,
`against_sales_order` varchar(140),
`so_detail` varchar(140),
`against_sales_invoice` varchar(140),
`si_detail` varchar(140),
`dn_detail` varchar(140),
`against_pick_list` varchar(140),
`pick_list_item` varchar(140),
`serial_and_batch_bundle` varchar(140),
`use_serial_batch_fields` int(1) not null default 0,
`serial_no` text,
`batch_no` varchar(140),
`actual_qty` decimal(21,9) not null default 0,
`actual_batch_qty` decimal(21,9) not null default 0,
`company_total_stock` decimal(21,9) not null default 0,
`installed_qty` decimal(21,9) not null default 0,
`packed_qty` decimal(21,9) not null default 0,
`received_qty` decimal(21,9) not null default 0,
`expense_account` varchar(140),
`item_tax_rate` text,
`material_request` varchar(140),
`purchase_order` varchar(140),
`purchase_order_item` varchar(140),
`material_request_item` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`page_break` int(1) not null default 0,
index `item_code`(`item_code`),
index `against_sales_order`(`against_sales_order`),
index `so_detail`(`so_detail`),
index `against_sales_invoice`(`against_sales_invoice`),
index `si_detail`(`si_detail`),
index `dn_detail`(`dn_detail`),
index `against_pick_list`(`against_pick_list`),
index `serial_and_batch_bundle`(`serial_and_batch_bundle`),
index `batch_no`(`batch_no`),
index `purchase_order`(`purchase_order`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:30,559 WARNING database DDL Query made to DB:
create table `tabPick List Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`description` text,
`item_group` varchar(140),
`warehouse` varchar(140),
`qty` decimal(21,9) not null default 1.0,
`stock_qty` decimal(21,9) not null default 0,
`picked_qty` decimal(21,9) not null default 0,
`stock_reserved_qty` decimal(21,9) not null default 0,
`uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`delivered_qty` decimal(21,9) not null default 0,
`serial_and_batch_bundle` varchar(140),
`use_serial_batch_fields` int(1) not null default 0,
`serial_no` text,
`batch_no` varchar(140),
`sales_order` varchar(140),
`sales_order_item` varchar(140),
`product_bundle_item` varchar(140),
`material_request` varchar(140),
`material_request_item` varchar(140),
index `item_code`(`item_code`),
index `serial_and_batch_bundle`(`serial_and_batch_bundle`),
index `batch_no`(`batch_no`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:30,731 WARNING database DDL Query made to DB:
create table `tabInventory Dimension` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`dimension_name` varchar(140) unique,
`reference_document` varchar(140),
`disabled` int(1) not null default 0,
`source_fieldname` varchar(140),
`target_fieldname` varchar(140),
`apply_to_all_doctypes` int(1) not null default 1,
`validate_negative_stock` int(1) not null default 0,
`document_type` varchar(140),
`type_of_transaction` varchar(140),
`fetch_from_parent` varchar(140),
`istable` int(1) not null default 0,
`condition` longtext,
`reqd` int(1) not null default 0,
`mandatory_depends_on` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:30,965 WARNING database DDL Query made to DB:
create table `tabMaterial Request Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`schedule_date` date,
`description` longtext,
`item_group` varchar(140),
`brand` varchar(140),
`image` text,
`qty` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`from_warehouse` varchar(140),
`warehouse` varchar(140),
`uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`stock_qty` decimal(21,9) not null default 0,
`min_order_qty` decimal(21,9) not null default 0,
`projected_qty` decimal(21,9) not null default 0,
`actual_qty` decimal(21,9) not null default 0,
`ordered_qty` decimal(21,9) not null default 0,
`received_qty` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`price_list_rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`expense_account` varchar(140),
`wip_composite_asset` varchar(140),
`manufacturer` varchar(140),
`manufacturer_part_no` varchar(140),
`bom_no` varchar(140),
`project` varchar(140),
`cost_center` varchar(140),
`lead_time_date` date,
`sales_order` varchar(140),
`sales_order_item` varchar(140),
`production_plan` varchar(140),
`material_request_plan_item` varchar(140),
`job_card_item` varchar(140),
`page_break` int(1) not null default 0,
index `item_code`(`item_code`),
index `item_name`(`item_name`),
index `item_group`(`item_group`),
index `sales_order_item`(`sales_order_item`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:31,031 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Item`
				ADD INDEX IF NOT EXISTS `item_code_warehouse_index`(item_code, warehouse)
2025-07-04 17:32:31,152 WARNING database DDL Query made to DB:
create table `tabDelivery Stop` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`customer` varchar(140),
`address` varchar(140),
`locked` int(1) not null default 0,
`customer_address` text,
`visited` int(1) not null default 0,
`delivery_note` varchar(140),
`grand_total` decimal(21,9) not null default 0,
`contact` varchar(140),
`email_sent_to` varchar(140),
`customer_contact` text,
`distance` decimal(21,9) not null default 0,
`estimated_arrival` datetime(6),
`lat` decimal(21,9) not null default 0,
`uom` varchar(140),
`lng` decimal(21,9) not null default 0,
`details` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:31,258 WARNING database DDL Query made to DB:
create table `tabItem Variant` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_attribute` varchar(140),
`item_attribute_value` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:31,378 WARNING database DDL Query made to DB:
create table `tabLanded Cost Taxes and Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`expense_account` varchar(140),
`account_currency` varchar(140),
`exchange_rate` decimal(21,9) not null default 0,
`description` text,
`amount` decimal(21,9) not null default 0,
`base_amount` decimal(21,9) not null default 0,
`has_corrective_cost` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:31,570 WARNING database DDL Query made to DB:
create table `tabStock Ledger Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`warehouse` varchar(140),
`posting_date` date,
`posting_time` time(6),
`posting_datetime` datetime(6),
`is_adjustment_entry` int(1) not null default 0,
`auto_created_serial_and_batch_bundle` int(1) not null default 0,
`voucher_type` varchar(140),
`voucher_no` varchar(140),
`voucher_detail_no` varchar(140),
`serial_and_batch_bundle` varchar(140),
`dependant_sle_voucher_detail_no` varchar(140),
`recalculate_rate` int(1) not null default 0,
`actual_qty` decimal(21,9) not null default 0,
`qty_after_transaction` decimal(21,9) not null default 0,
`incoming_rate` decimal(21,9) not null default 0,
`outgoing_rate` decimal(21,9) not null default 0,
`valuation_rate` decimal(21,9) not null default 0,
`stock_value` decimal(21,9) not null default 0,
`stock_value_difference` decimal(21,9) not null default 0,
`stock_queue` longtext,
`company` varchar(140),
`stock_uom` varchar(140),
`project` varchar(140),
`fiscal_year` varchar(140),
`has_batch_no` int(1) not null default 0,
`has_serial_no` int(1) not null default 0,
`is_cancelled` int(1) not null default 0,
`to_rename` int(1) not null default 1,
`serial_no` longtext,
`batch_no` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `posting_datetime`(`posting_datetime`),
index `voucher_type`(`voucher_type`),
index `voucher_detail_no`(`voucher_detail_no`),
index `serial_and_batch_bundle`(`serial_and_batch_bundle`),
index `to_rename`(`to_rename`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:31,658 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry`
				ADD INDEX IF NOT EXISTS `voucher_no_voucher_type_index`(voucher_no, voucher_type)
2025-07-04 17:32:31,685 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry`
				ADD INDEX IF NOT EXISTS `batch_no_item_code_warehouse_index`(batch_no, item_code, warehouse)
2025-07-04 17:32:31,718 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry`
				ADD INDEX IF NOT EXISTS `item_code_warehouse_posting_datetime_creation_index`(item_code, warehouse, posting_datetime, creation)
2025-07-04 17:32:31,804 WARNING database DDL Query made to DB:
create table `tabPacking Slip Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`batch_no` varchar(140),
`description` longtext,
`qty` decimal(21,9) not null default 0,
`net_weight` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`weight_uom` varchar(140),
`page_break` int(1) not null default 0,
`dn_detail` varchar(140),
`pi_detail` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:31,943 WARNING database DDL Query made to DB:
create table `tabSerial and Batch Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`serial_no` varchar(140),
`batch_no` varchar(140),
`qty` decimal(21,9) not null default 1.0,
`warehouse` varchar(140),
`delivered_qty` decimal(21,9) not null default 0,
`incoming_rate` decimal(21,9) not null default 0,
`outgoing_rate` decimal(21,9) not null default 0,
`stock_value_difference` decimal(21,9) not null default 0,
`is_outward` int(1) not null default 0,
`stock_queue` text,
index `serial_no`(`serial_no`),
index `batch_no`(`batch_no`),
index `warehouse`(`warehouse`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:32,078 WARNING database DDL Query made to DB:
create table `tabItem Quality Inspection Parameter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`specification` varchar(140),
`parameter_group` varchar(140),
`value` varchar(140),
`numeric` int(1) not null default 1,
`min_value` decimal(21,9) not null default 0,
`max_value` decimal(21,9) not null default 0,
`formula_based_criteria` int(1) not null default 0,
`acceptance_formula` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:32,219 WARNING database DDL Query made to DB:
create table `tabClosing Stock Balance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`company` varchar(140),
`status` varchar(140) default 'Draft',
`from_date` date,
`to_date` date,
`item_code` varchar(140),
`item_group` varchar(140),
`include_uom` varchar(140),
`warehouse` varchar(140),
`warehouse_type` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:32,431 WARNING database DDL Query made to DB:
create table `tabPick List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`company` varchar(140),
`purpose` varchar(140) default 'Material Transfer for Manufacture',
`customer` varchar(140),
`customer_name` varchar(140),
`work_order` varchar(140),
`material_request` varchar(140),
`for_qty` decimal(21,9) not null default 0,
`parent_warehouse` varchar(140),
`consider_rejected_warehouses` int(1) not null default 0,
`pick_manually` int(1) not null default 0,
`ignore_pricing_rule` int(1) not null default 0,
`scan_barcode` varchar(140),
`scan_mode` int(1) not null default 0,
`prompt_qty` int(1) not null default 0,
`amended_from` varchar(140),
`group_same_items` int(1) not null default 0,
`status` varchar(140) default 'Draft',
`delivery_status` varchar(140),
`per_delivered` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `status`(`status`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:32,692 WARNING database DDL Query made to DB:
create table `tabStock Reconciliation Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`barcode` varchar(140),
`has_item_scanned` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`item_group` varchar(140),
`warehouse` varchar(140),
`qty` decimal(21,9) not null default 0,
`valuation_rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`allow_zero_valuation_rate` int(1) not null default 0,
`use_serial_batch_fields` int(1) not null default 0,
`reconcile_all_serial_batch` int(1) not null default 0,
`serial_and_batch_bundle` varchar(140),
`current_serial_and_batch_bundle` varchar(140),
`serial_no` longtext,
`batch_no` varchar(140),
`current_qty` decimal(21,9) not null default 0,
`current_amount` decimal(21,9) not null default 0,
`current_valuation_rate` decimal(21,9) not null default 0,
`current_serial_no` longtext,
`quantity_difference` varchar(140),
`amount_difference` decimal(21,9) not null default 0,
index `serial_and_batch_bundle`(`serial_and_batch_bundle`),
index `batch_no`(`batch_no`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:33,085 WARNING database DDL Query made to DB:
create table `tabPurchase Receipt` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) default '{supplier_name}',
`naming_series` varchar(140),
`supplier` varchar(140),
`supplier_name` varchar(140),
`supplier_delivery_note` varchar(140),
`subcontracting_receipt` varchar(140),
`posting_date` date,
`posting_time` time(6),
`set_posting_time` int(1) not null default 0,
`company` varchar(140),
`apply_putaway_rule` int(1) not null default 0,
`is_return` int(1) not null default 0,
`return_against` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`currency` varchar(140),
`conversion_rate` decimal(21,9) not null default 0,
`buying_price_list` varchar(140),
`price_list_currency` varchar(140),
`plc_conversion_rate` decimal(21,9) not null default 0,
`ignore_pricing_rule` int(1) not null default 0,
`scan_barcode` varchar(140),
`set_warehouse` varchar(140),
`set_from_warehouse` varchar(140),
`rejected_warehouse` varchar(140),
`is_subcontracted` int(1) not null default 0,
`supplier_warehouse` varchar(140),
`total_qty` decimal(21,9) not null default 0,
`total_net_weight` decimal(21,9) not null default 0,
`base_total` decimal(21,9) not null default 0,
`base_net_total` decimal(21,9) not null default 0,
`total` decimal(21,9) not null default 0,
`net_total` decimal(21,9) not null default 0,
`tax_withholding_net_total` decimal(21,9) not null default 0,
`base_tax_withholding_net_total` decimal(21,9) not null default 0,
`tax_category` varchar(140),
`taxes_and_charges` varchar(140),
`shipping_rule` varchar(140),
`incoterm` varchar(140),
`named_place` varchar(140),
`base_taxes_and_charges_added` decimal(21,9) not null default 0,
`base_taxes_and_charges_deducted` decimal(21,9) not null default 0,
`base_total_taxes_and_charges` decimal(21,9) not null default 0,
`taxes_and_charges_added` decimal(21,9) not null default 0,
`taxes_and_charges_deducted` decimal(21,9) not null default 0,
`total_taxes_and_charges` decimal(21,9) not null default 0,
`base_grand_total` decimal(21,9) not null default 0,
`base_rounding_adjustment` decimal(21,9) not null default 0,
`base_rounded_total` decimal(21,9) not null default 0,
`base_in_words` varchar(240),
`grand_total` decimal(21,9) not null default 0,
`rounding_adjustment` decimal(21,9) not null default 0,
`rounded_total` decimal(21,9) not null default 0,
`in_words` varchar(240),
`disable_rounded_total` int(1) not null default 0,
`apply_discount_on` varchar(140) default 'Grand Total',
`base_discount_amount` decimal(21,9) not null default 0,
`additional_discount_percentage` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`other_charges_calculation` longtext,
`supplier_address` varchar(140),
`address_display` text,
`contact_person` varchar(140),
`contact_display` text,
`contact_mobile` text,
`contact_email` text,
`dispatch_address` varchar(140),
`dispatch_address_display` longtext,
`shipping_address` varchar(140),
`shipping_address_display` text,
`billing_address` varchar(140),
`billing_address_display` text,
`tc_name` varchar(140),
`terms` longtext,
`status` varchar(140) default 'Draft',
`per_billed` decimal(21,9) not null default 0,
`per_returned` decimal(21,9) not null default 0,
`auto_repeat` varchar(140),
`letter_head` varchar(140),
`group_same_items` int(1) not null default 0,
`select_print_heading` varchar(140),
`language` varchar(140),
`transporter_name` varchar(140),
`lr_no` varchar(140),
`lr_date` date,
`instructions` text,
`is_internal_supplier` int(1) not null default 0,
`represents_company` varchar(140),
`inter_company_reference` varchar(140),
`remarks` text,
`range` varchar(140),
`amended_from` varchar(140),
`is_old_subcontracting_flow` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `supplier`(`supplier`),
index `subcontracting_receipt`(`subcontracting_receipt`),
index `posting_date`(`posting_date`),
index `return_against`(`return_against`),
index `status`(`status`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:33,240 WARNING database DDL Query made to DB:
create table `tabQuality Inspection Parameter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`parameter` varchar(140) unique,
`parameter_group` varchar(140),
`description` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:33,390 WARNING database DDL Query made to DB:
create table `tabItem Tax` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_tax_template` varchar(140),
`tax_category` varchar(140),
`valid_from` date,
`minimum_net_rate` decimal(21,9) not null default 0,
`maximum_net_rate` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:33,525 WARNING database DDL Query made to DB:
create table `tabPrice List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enabled` int(1) not null default 1,
`price_list_name` varchar(140) unique,
`currency` varchar(140),
`buying` int(1) not null default 0,
`selling` int(1) not null default 0,
`price_not_uom_dependent` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:33,814 WARNING database DDL Query made to DB:
create table `tabStock Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`stock_entry_type` varchar(140),
`outgoing_stock_entry` varchar(140),
`purpose` varchar(140),
`add_to_transit` int(1) not null default 0,
`work_order` varchar(140),
`purchase_order` varchar(140),
`subcontracting_order` varchar(140),
`delivery_note_no` varchar(140),
`sales_invoice_no` varchar(140),
`pick_list` varchar(140),
`purchase_receipt_no` varchar(140),
`asset_repair` varchar(140),
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`set_posting_time` int(1) not null default 0,
`inspection_required` int(1) not null default 0,
`apply_putaway_rule` int(1) not null default 0,
`from_bom` int(1) not null default 0,
`use_multi_level_bom` int(1) not null default 1,
`bom_no` varchar(140),
`fg_completed_qty` decimal(21,9) not null default 0,
`process_loss_percentage` decimal(21,9) not null default 0,
`process_loss_qty` decimal(21,9) not null default 0,
`from_warehouse` varchar(140),
`source_warehouse_address` varchar(140),
`source_address_display` text,
`to_warehouse` varchar(140),
`target_warehouse_address` varchar(140),
`target_address_display` text,
`scan_barcode` varchar(140),
`total_outgoing_value` decimal(21,9) not null default 0,
`total_incoming_value` decimal(21,9) not null default 0,
`value_difference` decimal(21,9) not null default 0,
`total_additional_costs` decimal(21,9) not null default 0,
`supplier` varchar(140),
`supplier_name` varchar(140),
`supplier_address` varchar(140),
`address_display` text,
`project` varchar(140),
`select_print_heading` varchar(140),
`letter_head` varchar(140),
`is_opening` varchar(140),
`remarks` text,
`per_transferred` decimal(21,9) not null default 0,
`total_amount` decimal(21,9) not null default 0,
`job_card` varchar(140),
`amended_from` varchar(140),
`credit_note` varchar(140),
`is_return` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `stock_entry_type`(`stock_entry_type`),
index `purpose`(`purpose`),
index `work_order`(`work_order`),
index `delivery_note_no`(`delivery_note_no`),
index `pick_list`(`pick_list`),
index `purchase_receipt_no`(`purchase_receipt_no`),
index `posting_date`(`posting_date`),
index `job_card`(`job_card`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:34,189 WARNING database DDL Query made to DB:
create table `tabLanded Cost Voucher` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`company` varchar(140),
`posting_date` date,
`total_taxes_and_charges` decimal(21,9) not null default 0,
`distribute_charges_based_on` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:34,360 WARNING database DDL Query made to DB:
create table `tabQuality Inspection Reading` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`specification` varchar(140),
`parameter_group` varchar(140),
`status` varchar(140) default 'Accepted',
`value` varchar(140),
`numeric` int(1) not null default 1,
`manual_inspection` int(1) not null default 0,
`min_value` decimal(21,9) not null default 0,
`max_value` decimal(21,9) not null default 0,
`formula_based_criteria` int(1) not null default 0,
`acceptance_formula` longtext,
`reading_value` varchar(140),
`reading_1` varchar(140),
`reading_2` varchar(140),
`reading_3` varchar(140),
`reading_4` varchar(140),
`reading_5` varchar(140),
`reading_6` varchar(140),
`reading_7` varchar(140),
`reading_8` varchar(140),
`reading_9` varchar(140),
`reading_10` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:34,510 WARNING database DDL Query made to DB:
create table `tabStock Reconciliation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`company` varchar(140),
`purpose` varchar(140),
`posting_date` date,
`posting_time` time(6),
`set_posting_time` int(1) not null default 0,
`set_warehouse` varchar(140),
`scan_barcode` varchar(140),
`scan_mode` int(1) not null default 0,
`expense_account` varchar(140),
`difference_amount` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`cost_center` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:34,640 WARNING database DDL Query made to DB:
create table `tabLanded Cost Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`description` longtext,
`receipt_document_type` varchar(140),
`receipt_document` varchar(140),
`qty` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`is_fixed_asset` int(1) not null default 0,
`applicable_charges` decimal(21,9) not null default 0,
`purchase_receipt_item` varchar(140),
`cost_center` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:34,749 WARNING database DDL Query made to DB:
create table `tabItem Supplier` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`supplier` varchar(140),
`supplier_part_no` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:34,859 WARNING database DDL Query made to DB:
create table `tabItem Alternative` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`alternative_item_code` varchar(140),
`two_way` int(1) not null default 0,
`item_name` varchar(140),
`alternative_item_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:34,998 WARNING database DDL Query made to DB:
create table `tabCustoms Tariff Number` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`tariff_number` varchar(140) unique,
`description` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:35,132 WARNING database DDL Query made to DB:
create table `tabItem Reorder` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`warehouse_group` varchar(140),
`warehouse` varchar(140),
`warehouse_reorder_level` decimal(21,9) not null default 0,
`warehouse_reorder_qty` decimal(21,9) not null default 0,
`material_request_type` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:35,383 WARNING database DDL Query made to DB:
create table `tabStock Entry Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`barcode` varchar(140),
`has_item_scanned` int(1) not null default 0,
`s_warehouse` varchar(140),
`t_warehouse` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`is_finished_item` int(1) not null default 0,
`is_scrap_item` int(1) not null default 0,
`quality_inspection` varchar(140),
`subcontracted_item` varchar(140),
`description` longtext,
`item_group` varchar(140),
`image` text,
`qty` decimal(21,9) not null default 0,
`transfer_qty` decimal(21,9) not null default 0,
`retain_sample` int(1) not null default 0,
`uom` varchar(140),
`stock_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`sample_quantity` int(11) not null default 0,
`basic_rate` decimal(21,9) not null default 0,
`additional_cost` decimal(21,9) not null default 0,
`valuation_rate` decimal(21,9) not null default 0,
`allow_zero_valuation_rate` int(1) not null default 0,
`set_basic_rate_manually` int(1) not null default 0,
`basic_amount` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`use_serial_batch_fields` int(1) not null default 0,
`serial_and_batch_bundle` varchar(140),
`serial_no` text,
`batch_no` varchar(140),
`expense_account` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`actual_qty` decimal(21,9) not null default 0,
`transferred_qty` decimal(21,9) not null default 0,
`bom_no` varchar(140),
`allow_alternative_item` int(1) not null default 0,
`material_request` varchar(140),
`material_request_item` varchar(140),
`original_item` varchar(140),
`against_stock_entry` varchar(140),
`ste_detail` varchar(140),
`po_detail` varchar(140),
`sco_rm_detail` varchar(140),
`putaway_rule` varchar(140),
`reference_purchase_receipt` varchar(140),
`job_card_item` varchar(140),
index `item_code`(`item_code`),
index `serial_and_batch_bundle`(`serial_and_batch_bundle`),
index `actual_qty`(`actual_qty`),
index `material_request`(`material_request`),
index `job_card_item`(`job_card_item`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:35,521 WARNING database DDL Query made to DB:
create table `tabItem Variant Attribute` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`variant_of` varchar(140),
`attribute` varchar(140),
`attribute_value` varchar(140),
`numeric_values` int(1) not null default 0,
`disabled` int(1) not null default 0,
`from_range` decimal(21,9) not null default 0,
`increment` decimal(21,9) not null default 0,
`to_range` decimal(21,9) not null default 0,
index `variant_of`(`variant_of`),
index `attribute`(`attribute`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:35,656 WARNING database DDL Query made to DB:
create table `tabItem Attribute` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`attribute_name` varchar(140) unique,
`numeric_values` int(1) not null default 0,
`disabled` int(1) not null default 0,
`from_range` decimal(21,9) not null default 0,
`increment` decimal(21,9) not null default 0,
`to_range` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:36,063 WARNING database DDL Query made to DB:
create table `tabDelivery Note` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) default '{customer_name}',
`naming_series` varchar(140),
`customer` varchar(140),
`tax_id` varchar(140),
`customer_name` varchar(140),
`posting_date` date,
`posting_time` time(6),
`set_posting_time` int(1) not null default 0,
`company` varchar(140),
`amended_from` varchar(140),
`is_return` int(1) not null default 0,
`issue_credit_note` int(1) not null default 0,
`return_against` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`currency` varchar(140),
`conversion_rate` decimal(21,9) not null default 0,
`selling_price_list` varchar(140),
`price_list_currency` varchar(140),
`plc_conversion_rate` decimal(21,9) not null default 0,
`ignore_pricing_rule` int(1) not null default 0,
`scan_barcode` varchar(140),
`set_warehouse` varchar(140),
`set_target_warehouse` varchar(140),
`total_qty` decimal(21,9) not null default 0,
`total_net_weight` decimal(21,9) not null default 0,
`base_total` decimal(21,9) not null default 0,
`base_net_total` decimal(21,9) not null default 0,
`total` decimal(21,9) not null default 0,
`net_total` decimal(21,9) not null default 0,
`tax_category` varchar(140),
`taxes_and_charges` varchar(140),
`shipping_rule` varchar(140),
`incoterm` varchar(140),
`named_place` varchar(140),
`base_total_taxes_and_charges` decimal(21,9) not null default 0,
`total_taxes_and_charges` decimal(21,9) not null default 0,
`base_grand_total` decimal(21,9) not null default 0,
`base_rounding_adjustment` decimal(21,9) not null default 0,
`base_rounded_total` decimal(21,9) not null default 0,
`base_in_words` varchar(240),
`grand_total` decimal(21,9) not null default 0,
`rounding_adjustment` decimal(21,9) not null default 0,
`rounded_total` decimal(21,9) not null default 0,
`in_words` varchar(240),
`disable_rounded_total` int(1) not null default 0,
`apply_discount_on` varchar(140) default 'Grand Total',
`base_discount_amount` decimal(21,9) not null default 0,
`additional_discount_percentage` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`other_charges_calculation` longtext,
`customer_address` varchar(140),
`address_display` text,
`contact_person` varchar(140),
`contact_display` text,
`contact_mobile` text,
`contact_email` varchar(140),
`shipping_address_name` varchar(140),
`shipping_address` text,
`dispatch_address_name` varchar(140),
`dispatch_address` text,
`company_address` varchar(140),
`company_address_display` text,
`company_contact_person` varchar(140),
`tc_name` varchar(140),
`terms` longtext,
`per_billed` decimal(21,9) not null default 0,
`status` varchar(140) default 'Draft',
`per_installed` decimal(21,9) not null default 0,
`installation_status` varchar(140),
`per_returned` decimal(21,9) not null default 0,
`transporter` varchar(140),
`driver` varchar(140),
`lr_no` varchar(140),
`vehicle_no` varchar(140),
`transporter_name` varchar(140),
`driver_name` varchar(140),
`lr_date` date,
`po_no` text,
`po_date` date,
`sales_partner` varchar(140),
`amount_eligible_for_commission` decimal(21,9) not null default 0,
`commission_rate` decimal(21,9) not null default 0,
`total_commission` decimal(21,9) not null default 0,
`auto_repeat` varchar(140),
`letter_head` varchar(140),
`print_without_amount` int(1) not null default 0,
`group_same_items` int(1) not null default 0,
`select_print_heading` varchar(140),
`language` varchar(140),
`is_internal_customer` int(1) not null default 0,
`represents_company` varchar(140),
`inter_company_reference` varchar(140),
`customer_group` varchar(140),
`territory` varchar(140),
`source` varchar(140),
`campaign` varchar(140),
`excise_page` varchar(140),
`instructions` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `customer`(`customer`),
index `posting_date`(`posting_date`),
index `return_against`(`return_against`),
index `status`(`status`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:36,275 WARNING database DDL Query made to DB:
create table `tabWarehouse` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`warehouse_name` varchar(140),
`is_group` int(1) not null default 0,
`parent_warehouse` varchar(140),
`is_rejected_warehouse` int(1) not null default 0,
`account` varchar(140),
`company` varchar(140),
`email_id` varchar(140),
`phone_no` varchar(140),
`mobile_no` varchar(140),
`address_line_1` varchar(140),
`address_line_2` varchar(140),
`city` varchar(140),
`state` varchar(140),
`pin` varchar(140),
`warehouse_type` varchar(140),
`default_in_transit_warehouse` varchar(140),
`lft` int(11) not null default 0,
`rgt` int(11) not null default 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `parent_warehouse`(`parent_warehouse`),
index `company`(`company`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:36,435 WARNING database DDL Query made to DB:
create table `tabShipment Parcel Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`parcel_template_name` varchar(140) unique,
`length` int(11) not null default 0,
`width` int(11) not null default 0,
`height` int(11) not null default 0,
`weight` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:36,547 WARNING database DDL Query made to DB:
create table `tabItem Attribute Value` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`attribute_value` varchar(140),
`abbr` varchar(140),
index `abbr`(`abbr`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:36,701 WARNING database DDL Query made to DB:
create table `tabBin` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`warehouse` varchar(140),
`actual_qty` decimal(21,9) not null default 0,
`planned_qty` decimal(21,9) not null default 0,
`indented_qty` decimal(21,9) not null default 0,
`ordered_qty` decimal(21,9) not null default 0,
`projected_qty` decimal(21,9) not null default 0,
`reserved_qty` decimal(21,9) not null default 0,
`reserved_qty_for_production` decimal(21,9) not null default 0,
`reserved_qty_for_sub_contract` decimal(21,9) not null default 0,
`reserved_qty_for_production_plan` decimal(21,9) not null default 0,
`reserved_stock` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`valuation_rate` decimal(21,9) not null default 0,
`stock_value` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `warehouse`(`warehouse`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:36,809 WARNING database DDL Query made to DB:
alter table `tabBin`
					add unique `unique_item_warehouse`(item_code, warehouse)
2025-07-04 17:32:36,884 WARNING database DDL Query made to DB:
create table `tabLanded Cost Purchase Receipt` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`receipt_document_type` varchar(140),
`receipt_document` varchar(140),
`supplier` varchar(140),
`posting_date` date,
`grand_total` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:37,250 WARNING database DDL Query made to DB:
create table `tabPurchase Receipt Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`barcode` varchar(140),
`has_item_scanned` int(1) not null default 0,
`item_code` varchar(140),
`product_bundle` varchar(140),
`supplier_part_no` varchar(140),
`item_name` varchar(140),
`description` longtext,
`brand` varchar(140),
`item_group` varchar(140),
`image` text,
`received_qty` decimal(21,9) not null default 0,
`qty` decimal(21,9) not null default 0,
`rejected_qty` decimal(21,9) not null default 0,
`uom` varchar(140),
`stock_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`retain_sample` int(1) not null default 0,
`sample_quantity` int(11) not null default 0,
`received_stock_qty` decimal(21,9) not null default 0,
`stock_qty` decimal(21,9) not null default 0,
`returned_qty` decimal(21,9) not null default 0,
`price_list_rate` decimal(21,9) not null default 0,
`base_price_list_rate` decimal(21,9) not null default 0,
`margin_type` varchar(140),
`margin_rate_or_amount` decimal(21,9) not null default 0,
`rate_with_margin` decimal(21,9) not null default 0,
`discount_percentage` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`distributed_discount_amount` decimal(21,9) not null default 0,
`base_rate_with_margin` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`base_rate` decimal(21,9) not null default 0,
`base_amount` decimal(21,9) not null default 0,
`pricing_rules` text,
`stock_uom_rate` decimal(21,9) not null default 0,
`is_free_item` int(1) not null default 0,
`apply_tds` int(1) not null default 1,
`net_rate` decimal(21,9) not null default 0,
`net_amount` decimal(21,9) not null default 0,
`item_tax_template` varchar(140),
`base_net_rate` decimal(21,9) not null default 0,
`base_net_amount` decimal(21,9) not null default 0,
`valuation_rate` decimal(21,9) not null default 0,
`sales_incoming_rate` decimal(21,9) not null default 0,
`item_tax_amount` decimal(21,9) not null default 0,
`rm_supp_cost` decimal(21,9) not null default 0,
`landed_cost_voucher_amount` decimal(21,9) not null default 0,
`amount_difference_with_purchase_invoice` decimal(21,9) not null default 0,
`billed_amt` decimal(21,9) not null default 0,
`warehouse` varchar(140),
`rejected_warehouse` varchar(140),
`from_warehouse` varchar(140),
`material_request` varchar(140),
`purchase_order` varchar(140),
`purchase_invoice` varchar(140),
`allow_zero_valuation_rate` int(1) not null default 0,
`return_qty_from_rejected_warehouse` int(1) not null default 0,
`is_fixed_asset` int(1) not null default 0,
`asset_location` varchar(140),
`asset_category` varchar(140),
`schedule_date` date,
`quality_inspection` varchar(140),
`material_request_item` varchar(140),
`purchase_order_item` varchar(140),
`purchase_invoice_item` varchar(140),
`purchase_receipt_item` varchar(140),
`delivery_note_item` varchar(140),
`putaway_rule` varchar(140),
`serial_and_batch_bundle` varchar(140),
`use_serial_batch_fields` int(1) not null default 0,
`rejected_serial_and_batch_bundle` varchar(140),
`serial_no` text,
`rejected_serial_no` text,
`batch_no` varchar(140),
`include_exploded_items` int(1) not null default 0,
`bom` varchar(140),
`weight_per_unit` decimal(21,9) not null default 0,
`total_weight` decimal(21,9) not null default 0,
`weight_uom` varchar(140),
`manufacturer` varchar(140),
`manufacturer_part_no` varchar(140),
`expense_account` varchar(140),
`item_tax_rate` longtext,
`wip_composite_asset` varchar(140),
`provisional_expense_account` varchar(140),
`project` varchar(140),
`cost_center` varchar(140),
`page_break` int(1) not null default 0,
`sales_order` varchar(140),
`sales_order_item` varchar(140),
`subcontracting_receipt_item` varchar(140),
index `item_code`(`item_code`),
index `purchase_order`(`purchase_order`),
index `purchase_order_item`(`purchase_order_item`),
index `purchase_invoice_item`(`purchase_invoice_item`),
index `purchase_receipt_item`(`purchase_receipt_item`),
index `delivery_note_item`(`delivery_note_item`),
index `serial_and_batch_bundle`(`serial_and_batch_bundle`),
index `batch_no`(`batch_no`),
index `sales_order`(`sales_order`),
index `sales_order_item`(`sales_order_item`),
index `subcontracting_receipt_item`(`subcontracting_receipt_item`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:37,433 WARNING database DDL Query made to DB:
create table `tabQuality Inspection` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`company` varchar(140),
`report_date` date,
`status` varchar(140) default 'Accepted',
`child_row_reference` varchar(140),
`inspection_type` varchar(140),
`reference_type` varchar(140),
`reference_name` varchar(140),
`item_code` varchar(140),
`item_serial_no` varchar(140),
`batch_no` varchar(140),
`sample_size` decimal(21,9) not null default 0,
`item_name` varchar(140),
`description` text,
`bom_no` varchar(140),
`quality_inspection_template` varchar(140),
`manual_inspection` int(1) not null default 0,
`inspected_by` varchar(140) default 'user',
`verified_by` varchar(140),
`remarks` text,
`amended_from` varchar(140),
`letter_head` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `report_date`(`report_date`),
index `item_code`(`item_code`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:37,743 WARNING database DDL Query made to DB:
create table `tabSerial No` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`serial_no` varchar(140) unique,
`item_code` varchar(140),
`batch_no` varchar(140),
`warehouse` varchar(140),
`purchase_rate` decimal(21,9) not null default 0,
`status` varchar(140),
`item_name` varchar(140),
`description` text,
`item_group` varchar(140),
`brand` varchar(140),
`asset` varchar(140),
`asset_status` varchar(140),
`location` varchar(140),
`employee` varchar(140),
`warranty_expiry_date` date,
`amc_expiry_date` date,
`maintenance_status` varchar(140),
`warranty_period` int(11) not null default 0,
`company` varchar(140),
`work_order` varchar(140),
`purchase_document_no` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `maintenance_status`(`maintenance_status`),
index `company`(`company`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:37,880 WARNING database DDL Query made to DB:
create table `tabItem Barcode` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`barcode` varchar(140) unique,
`barcode_type` varchar(140),
`uom` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:37,994 WARNING database DDL Query made to DB:
create table `tabQuality Inspection Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`quality_inspection_template_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:38,141 WARNING database DDL Query made to DB:
create table `tabWarehouse Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:38,390 WARNING database DDL Query made to DB:
create table `tabMaterial Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`title` varchar(140) default '{material_request_type}',
`material_request_type` varchar(140),
`customer` varchar(140),
`company` varchar(140),
`transaction_date` date,
`schedule_date` date,
`amended_from` varchar(140),
`scan_barcode` varchar(140),
`set_from_warehouse` varchar(140),
`set_warehouse` varchar(140),
`tc_name` varchar(140),
`terms` longtext,
`status` varchar(140),
`per_ordered` decimal(21,9) not null default 0,
`transfer_status` varchar(140),
`per_received` decimal(21,9) not null default 0,
`letter_head` varchar(140),
`select_print_heading` varchar(140),
`job_card` varchar(140),
`work_order` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `company`(`company`),
index `transaction_date`(`transaction_date`),
index `status`(`status`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:38,590 WARNING database DDL Query made to DB:
create table `tabPacked Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`parent_item` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`description` longtext,
`warehouse` varchar(140),
`target_warehouse` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`qty` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`uom` varchar(140),
`use_serial_batch_fields` int(1) not null default 0,
`serial_and_batch_bundle` varchar(140),
`serial_no` text,
`batch_no` varchar(140),
`actual_batch_qty` decimal(21,9) not null default 0,
`actual_qty` decimal(21,9) not null default 0,
`projected_qty` decimal(21,9) not null default 0,
`ordered_qty` decimal(21,9) not null default 0,
`packed_qty` decimal(21,9) not null default 0,
`incoming_rate` decimal(21,9) not null default 0,
`picked_qty` decimal(21,9) not null default 0,
`page_break` int(1) not null default 0,
`prevdoc_doctype` varchar(140),
`parent_detail_docname` varchar(140),
index `parent_detail_docname`(`parent_detail_docname`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:38,656 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacked Item`
				ADD INDEX IF NOT EXISTS `item_code_warehouse_index`(item_code, warehouse)
2025-07-04 17:32:38,836 WARNING database DDL Query made to DB:
create table `tabShipment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`pickup_from_type` varchar(140) default 'Company',
`pickup_company` varchar(140),
`pickup_customer` varchar(140),
`pickup_supplier` varchar(140),
`pickup` varchar(140),
`pickup_address_name` varchar(140),
`pickup_address` text,
`pickup_contact_person` varchar(140),
`pickup_contact_name` varchar(140),
`pickup_contact_email` varchar(140),
`pickup_contact` text,
`delivery_to_type` varchar(140) default 'Customer',
`delivery_company` varchar(140),
`delivery_customer` varchar(140),
`delivery_supplier` varchar(140),
`delivery_to` varchar(140),
`delivery_address_name` varchar(140),
`delivery_address` text,
`delivery_contact_name` varchar(140),
`delivery_contact_email` varchar(140),
`delivery_contact` text,
`parcel_template` varchar(140),
`total_weight` decimal(21,9) not null default 0,
`pallets` varchar(140) default 'No',
`value_of_goods` decimal(21,9) not null default 0,
`pickup_date` date,
`pickup_from` time(6) default '09:00',
`pickup_to` time(6) default '17:00',
`shipment_type` varchar(140) default 'Goods',
`pickup_type` varchar(140) default 'Pickup',
`incoterm` varchar(140),
`description_of_content` text,
`service_provider` varchar(140),
`shipment_id` varchar(140),
`shipment_amount` decimal(21,9) not null default 0,
`status` varchar(140),
`tracking_url` text,
`carrier` varchar(140),
`carrier_service` varchar(140),
`awb_number` varchar(140),
`tracking_status` varchar(140),
`tracking_status_info` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:38,964 WARNING database DDL Query made to DB:
create table `tabUOM Conversion Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:39,123 WARNING database DDL Query made to DB:
create table `tabBatch` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`use_batchwise_valuation` int(1) not null default 0,
`batch_id` varchar(140) unique,
`item` varchar(140),
`item_name` varchar(140),
`image` text,
`parent_batch` varchar(140),
`manufacturing_date` date,
`batch_qty` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`expiry_date` date,
`supplier` varchar(140),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`description` text,
`qty_to_produce` decimal(21,9) not null default 0,
`produced_qty` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:39,236 WARNING database DDL Query made to DB:
create table `tabVariant Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`field_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:39,708 WARNING database DDL Query made to DB:
create table `tabStock Entry Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`purpose` varchar(140) default 'Material Issue',
`add_to_transit` int(1) not null default 0,
`is_standard` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:39,864 WARNING database DDL Query made to DB:
create table `tabItem Manufacturer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`manufacturer` varchar(140),
`manufacturer_part_no` varchar(140),
`item_name` varchar(140),
`description` text,
`is_default` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:40,042 WARNING database DDL Query made to DB:
create table `tabItem Price` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`uom` varchar(140),
`packing_unit` int(11) not null default 0,
`item_name` varchar(140),
`brand` varchar(140),
`item_description` text,
`price_list` varchar(140),
`customer` varchar(140),
`supplier` varchar(140),
`batch_no` varchar(140),
`buying` int(1) not null default 0,
`selling` int(1) not null default 0,
`currency` varchar(140),
`price_list_rate` decimal(21,9) not null default 0,
`valid_from` date,
`lead_time_days` int(11) not null default 0,
`valid_upto` date,
`note` text,
`reference` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `item_code`(`item_code`),
index `price_list`(`price_list`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:40,164 WARNING database DDL Query made to DB:
create table `tabItem Website Specification` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`label` varchar(140),
`description` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:40,414 WARNING database DDL Query made to DB:
create table `tabShipment Parcel` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`length` int(11) not null default 0,
`width` int(11) not null default 0,
`height` int(11) not null default 0,
`weight` decimal(21,9) not null default 0,
`count` int(11) not null default 1,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:40,738 WARNING database DDL Query made to DB:
create table `tabItem` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`item_code` varchar(140) unique,
`item_name` varchar(140),
`item_group` varchar(140),
`stock_uom` varchar(140),
`disabled` int(1) not null default 0,
`allow_alternative_item` int(1) not null default 0,
`is_stock_item` int(1) not null default 1,
`has_variants` int(1) not null default 0,
`opening_stock` decimal(21,9) not null default 0,
`valuation_rate` decimal(21,9) not null default 0,
`standard_rate` decimal(21,9) not null default 0,
`is_fixed_asset` int(1) not null default 0,
`auto_create_assets` int(1) not null default 0,
`is_grouped_asset` int(1) not null default 0,
`asset_category` varchar(140),
`asset_naming_series` varchar(140),
`over_delivery_receipt_allowance` decimal(21,9) not null default 0,
`over_billing_allowance` decimal(21,9) not null default 0,
`image` text,
`description` longtext,
`brand` varchar(140),
`shelf_life_in_days` int(11) not null default 0,
`end_of_life` date default '2099-12-31',
`default_material_request_type` varchar(140) default 'Purchase',
`valuation_method` varchar(140),
`warranty_period` varchar(140),
`weight_per_unit` decimal(21,9) not null default 0,
`weight_uom` varchar(140),
`allow_negative_stock` int(1) not null default 0,
`has_batch_no` int(1) not null default 0,
`create_new_batch` int(1) not null default 0,
`batch_number_series` varchar(140),
`has_expiry_date` int(1) not null default 0,
`retain_sample` int(1) not null default 0,
`sample_quantity` int(11) not null default 0,
`has_serial_no` int(1) not null default 0,
`serial_no_series` varchar(140),
`variant_of` varchar(140),
`variant_based_on` varchar(140) default 'Item Attribute',
`enable_deferred_expense` int(1) not null default 0,
`no_of_months_exp` int(11) not null default 0,
`enable_deferred_revenue` int(1) not null default 0,
`no_of_months` int(11) not null default 0,
`purchase_uom` varchar(140),
`min_order_qty` decimal(21,9) not null default 0,
`safety_stock` decimal(21,9) not null default 0,
`is_purchase_item` int(1) not null default 1,
`lead_time_days` int(11) not null default 0,
`last_purchase_rate` decimal(21,9) not null default 0,
`is_customer_provided_item` int(1) not null default 0,
`customer` varchar(140),
`delivered_by_supplier` int(1) not null default 0,
`country_of_origin` varchar(140),
`customs_tariff_number` varchar(140),
`sales_uom` varchar(140),
`grant_commission` int(1) not null default 1,
`is_sales_item` int(1) not null default 1,
`max_discount` decimal(21,9) not null default 0,
`inspection_required_before_purchase` int(1) not null default 0,
`quality_inspection_template` varchar(140),
`inspection_required_before_delivery` int(1) not null default 0,
`include_item_in_manufacturing` int(1) not null default 1,
`is_sub_contracted_item` int(1) not null default 0,
`default_bom` varchar(140),
`customer_code` text,
`default_item_manufacturer` varchar(140),
`default_manufacturer_part_no` varchar(140),
`total_projected_qty` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `item_name`(`item_name`),
index `item_group`(`item_group`),
index `disabled`(`disabled`),
index `variant_of`(`variant_of`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:40,991 WARNING database DDL Query made to DB:
create table `tabStock Reservation Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`warehouse` varchar(140),
`has_serial_no` int(1) not null default 0,
`has_batch_no` int(1) not null default 0,
`voucher_type` varchar(140),
`voucher_no` varchar(140),
`voucher_detail_no` varchar(140),
`from_voucher_type` varchar(140),
`from_voucher_no` varchar(140),
`from_voucher_detail_no` varchar(140),
`stock_uom` varchar(140),
`available_qty` decimal(21,9) not null default 0,
`voucher_qty` decimal(21,9) not null default 0,
`reserved_qty` decimal(21,9) not null default 0,
`delivered_qty` decimal(21,9) not null default 0,
`reservation_based_on` varchar(140) default 'Qty',
`company` varchar(140),
`project` varchar(140),
`status` varchar(140) default 'Draft',
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `item_code`(`item_code`),
index `warehouse`(`warehouse`),
index `voucher_no`(`voucher_no`),
index `voucher_detail_no`(`voucher_detail_no`),
index `from_voucher_no`(`from_voucher_no`),
index `company`(`company`),
index `project`(`project`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:41,194 WARNING database DDL Query made to DB:
create table `tabRepost Item Valuation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`based_on` varchar(140) default 'Transaction',
`voucher_type` varchar(140),
`voucher_no` varchar(140),
`item_code` varchar(140),
`warehouse` varchar(140),
`posting_date` date,
`posting_time` time(6),
`status` varchar(140) default 'Queued',
`company` varchar(140),
`allow_negative_stock` int(1) not null default 1,
`via_landed_cost_voucher` int(1) not null default 0,
`allow_zero_rate` int(1) not null default 0,
`recreate_stock_ledgers` int(1) not null default 0,
`amended_from` varchar(140),
`error_log` longtext,
`reposting_data_file` text,
`items_to_be_repost` longtext,
`distinct_item_and_warehouse` longtext,
`total_reposting_count` int(11) not null default 0,
`current_index` int(11) not null default 0,
`gl_reposting_index` int(11) not null default 0,
`affected_transactions` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:41,290 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Item Valuation`
				ADD INDEX IF NOT EXISTS `item_warehouse`(warehouse, item_code)
2025-07-04 17:32:41,449 WARNING database DDL Query made to DB:
create table `tabSerial and Batch Bundle` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140) default 'SABB-.########',
`company` varchar(140),
`item_name` varchar(140),
`has_serial_no` int(1) not null default 0,
`has_batch_no` int(1) not null default 0,
`item_code` varchar(140),
`warehouse` varchar(140),
`type_of_transaction` varchar(140),
`total_qty` decimal(21,9) not null default 0,
`item_group` varchar(140),
`avg_rate` decimal(21,9) not null default 0,
`total_amount` decimal(21,9) not null default 0,
`voucher_type` varchar(140),
`voucher_no` varchar(140),
`voucher_detail_no` varchar(140),
`posting_date` date,
`posting_time` time(6),
`returned_against` varchar(140),
`is_cancelled` int(1) not null default 0,
`is_rejected` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `company`(`company`),
index `item_code`(`item_code`),
index `warehouse`(`warehouse`),
index `type_of_transaction`(`type_of_transaction`),
index `voucher_no`(`voucher_no`),
index `voucher_detail_no`(`voucher_detail_no`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:42,643 WARNING database DDL Query made to DB:
create table `tabIssue Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:42,900 WARNING database DDL Query made to DB:
create table `tabIssue` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`subject` varchar(140),
`customer` varchar(140),
`raised_by` varchar(140),
`status` varchar(140) default 'Open',
`priority` varchar(140),
`issue_type` varchar(140),
`issue_split_from` varchar(140),
`description` longtext,
`service_level_agreement` varchar(140),
`response_by` datetime(6),
`agreement_status` varchar(140) default 'First Response Due',
`sla_resolution_by` datetime(6),
`service_level_agreement_creation` datetime(6),
`on_hold_since` datetime(6),
`total_hold_time` decimal(21,9),
`first_response_time` decimal(21,9),
`first_responded_on` datetime(6),
`avg_response_time` decimal(21,9),
`resolution_details` longtext,
`opening_date` date,
`opening_time` time(6),
`sla_resolution_date` datetime(6),
`resolution_time` decimal(21,9),
`user_resolution_time` decimal(21,9),
`lead` varchar(140),
`contact` varchar(140),
`email_account` varchar(140),
`customer_name` varchar(140),
`project` varchar(140),
`company` varchar(140),
`via_customer_portal` int(1) not null default 0,
`attachment` text,
`content_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `customer`(`customer`),
index `status`(`status`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:43,059 WARNING database DDL Query made to DB:
create table `tabSupport Search Source` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source_name` varchar(140),
`source_type` varchar(140),
`base_url` varchar(140),
`query_route` varchar(140),
`search_term_param_name` varchar(140),
`response_result_key_path` varchar(140),
`post_route` varchar(140),
`post_route_key_list` varchar(140),
`post_title_key` varchar(140),
`post_description_key` varchar(140),
`source_doctype` varchar(140),
`result_title_field` varchar(140),
`result_preview_field` varchar(140),
`result_route_field` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:43,165 WARNING database DDL Query made to DB:
create table `tabPause SLA On Status` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:43,409 WARNING database DDL Query made to DB:
create table `tabService Level Priority` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`default_priority` int(1) not null default 0,
`priority` varchar(140),
`response_time` decimal(21,9),
`resolution_time` decimal(21,9),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:43,632 WARNING database DDL Query made to DB:
create table `tabWarranty Claim` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`status` varchar(140) default 'Open',
`complaint_date` date,
`customer` varchar(140),
`serial_no` varchar(140),
`complaint` longtext,
`item_code` varchar(140),
`item_name` varchar(140),
`description` text,
`warranty_amc_status` varchar(140),
`warranty_expiry_date` date,
`amc_expiry_date` date,
`resolution_date` datetime(6),
`resolved_by` varchar(140),
`resolution_details` text,
`customer_name` varchar(140),
`contact_person` varchar(140),
`contact_display` text,
`contact_mobile` varchar(140),
`contact_email` varchar(140),
`territory` varchar(140),
`customer_group` varchar(140),
`customer_address` varchar(140),
`address_display` text,
`service_address` text,
`company` varchar(140),
`complaint_raised_by` varchar(140),
`from_company` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `status`(`status`),
index `complaint_date`(`complaint_date`),
index `customer`(`customer`),
index `serial_no`(`serial_no`),
index `item_code`(`item_code`),
index `warranty_amc_status`(`warranty_amc_status`),
index `resolution_date`(`resolution_date`),
index `resolved_by`(`resolved_by`),
index `territory`(`territory`),
index `company`(`company`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:43,768 WARNING database DDL Query made to DB:
create table `tabService Day` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`workday` varchar(140),
`start_time` time(6),
`end_time` time(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:43,878 WARNING database DDL Query made to DB:
create table `tabSLA Fulfilled On Status` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:44,045 WARNING database DDL Query made to DB:
create table `tabService Level Agreement` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`default_priority` varchar(140),
`service_level` varchar(140),
`enabled` int(1) not null default 1,
`default_service_level_agreement` int(1) not null default 0,
`entity_type` varchar(140),
`entity` varchar(140),
`condition` longtext,
`start_date` date,
`end_date` date,
`apply_sla_for_resolution` int(1) not null default 1,
`holiday_list` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:44,178 WARNING database DDL Query made to DB:
create table `tabIssue Priority` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:44,417 WARNING database DDL Query made to DB:
create table `tabPortal User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
index `user`(`user`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:44,776 WARNING database DDL Query made to DB:
create table `tabSMS Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`sender_name` varchar(140),
`sent_on` date,
`message` text,
`no_of_requested_sms` int(11) not null default 0,
`requested_numbers` longtext,
`no_of_sent_sms` int(11) not null default 0,
`sent_to` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:44,926 WARNING database DDL Query made to DB:
create table `tabVideo` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`provider` varchar(140),
`url` varchar(140),
`youtube_video_id` varchar(140),
`publish_date` date,
`duration` decimal(21,9),
`like_count` decimal(21,9) not null default 0,
`view_count` decimal(21,9) not null default 0,
`dislike_count` decimal(21,9) not null default 0,
`comment_count` decimal(21,9) not null default 0,
`description` longtext,
`image` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:45,353 WARNING database DDL Query made to DB:
create table `tabAsset Category Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company_name` varchar(140),
`fixed_asset_account` varchar(140),
`accumulated_depreciation_account` varchar(140),
`depreciation_expense_account` varchar(140),
`capital_work_in_progress_account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:45,476 WARNING database DDL Query made to DB:
create table `tabAsset Maintenance Team` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`maintenance_team_name` varchar(140) unique,
`maintenance_manager` varchar(140),
`maintenance_manager_name` varchar(140),
`company` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:45,600 WARNING database DDL Query made to DB:
create table `tabAsset Movement Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`asset` varchar(140),
`source_location` varchar(140),
`from_employee` varchar(140),
`asset_name` varchar(140),
`target_location` varchar(140),
`to_employee` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:45,840 WARNING database DDL Query made to DB:
create table `tabAsset` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`asset_owner` varchar(140),
`asset_owner_company` varchar(140),
`is_existing_asset` int(1) not null default 0,
`is_composite_asset` int(1) not null default 0,
`supplier` varchar(140),
`customer` varchar(140),
`image` text,
`journal_entry_for_scrap` varchar(140),
`naming_series` varchar(140),
`asset_name` varchar(140),
`asset_category` varchar(140),
`location` varchar(140),
`split_from` varchar(140),
`custodian` varchar(140),
`department` varchar(140),
`disposal_date` date,
`cost_center` varchar(140),
`purchase_receipt` varchar(140),
`purchase_receipt_item` varchar(140),
`purchase_invoice` varchar(140),
`purchase_invoice_item` varchar(140),
`purchase_date` date,
`available_for_use_date` date,
`gross_purchase_amount` decimal(21,9) not null default 0,
`asset_quantity` int(11) not null default 1,
`additional_asset_cost` decimal(21,9) not null default 0,
`total_asset_cost` decimal(21,9) not null default 0,
`calculate_depreciation` int(1) not null default 0,
`opening_accumulated_depreciation` decimal(21,9) not null default 0,
`opening_number_of_booked_depreciations` int(11) not null default 0,
`is_fully_depreciated` int(1) not null default 0,
`depreciation_method` varchar(140),
`value_after_depreciation` decimal(21,9) not null default 0,
`total_number_of_depreciations` int(11) not null default 0,
`frequency_of_depreciation` int(11) not null default 0,
`next_depreciation_date` date,
`policy_number` varchar(140),
`insurer` varchar(140),
`insured_value` varchar(140),
`insurance_start_date` date,
`insurance_end_date` date,
`comprehensive_insurance` varchar(140),
`maintenance_required` int(1) not null default 0,
`status` varchar(140) default 'Draft',
`booked_fixed_asset` int(1) not null default 0,
`purchase_amount` decimal(21,9) not null default 0,
`default_finance_book` varchar(140),
`depr_entry_posting_status` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:45,965 WARNING database DDL Query made to DB:
create table `tabAsset Shift Factor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`shift_name` varchar(140) unique,
`shift_factor` decimal(21,9) not null default 0,
`default` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:46,122 WARNING database DDL Query made to DB:
create table `tabAsset Capitalization Stock Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`warehouse` varchar(140),
`purchase_receipt_item` varchar(140),
`stock_qty` decimal(21,9) not null default 0,
`actual_qty` decimal(21,9) not null default 0,
`valuation_rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`serial_and_batch_bundle` varchar(140),
`use_serial_batch_fields` int(1) not null default 0,
`serial_no` text,
`batch_no` varchar(140),
`cost_center` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:46,245 WARNING database DDL Query made to DB:
create table `tabAsset Activity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset` varchar(140),
`date` datetime(6),
`user` varchar(140),
`subject` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:46,403 WARNING database DDL Query made to DB:
create table `tabAsset Maintenance Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset_maintenance` varchar(140),
`naming_series` varchar(140),
`asset_name` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`task` varchar(140),
`task_name` varchar(140),
`maintenance_type` varchar(140),
`periodicity` varchar(140),
`has_certificate` int(1) not null default 0,
`certificate_attachement` text,
`maintenance_status` varchar(140),
`assign_to_name` varchar(140),
`task_assignee_email` varchar(140),
`due_date` date,
`completion_date` date,
`description` varchar(140),
`actions_performed` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:46,532 WARNING database DDL Query made to DB:
create table `tabDepreciation Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`schedule_date` date,
`depreciation_amount` decimal(21,9) not null default 0,
`accumulated_depreciation_amount` decimal(21,9) not null default 0,
`journal_entry` varchar(140),
`shift` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:46,665 WARNING database DDL Query made to DB:
create table `tabAsset Capitalization Service Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`expense_account` varchar(140),
`qty` decimal(21,9) not null default 1.0,
`uom` varchar(140),
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`cost_center` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:46,802 WARNING database DDL Query made to DB:
create table `tabAsset Maintenance Task` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`maintenance_task` varchar(140),
`maintenance_type` varchar(140),
`maintenance_status` varchar(140),
`start_date` date,
`periodicity` varchar(140),
`end_date` date,
`certificate_required` int(1) not null default 0,
`assign_to` varchar(140),
`assign_to_name` varchar(140),
`next_due_date` date,
`last_completion_date` date,
`description` longtext,
index `certificate_required`(`certificate_required`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:46,981 WARNING database DDL Query made to DB:
create table `tabAsset Capitalization` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`naming_series` varchar(140),
`capitalization_method` varchar(140),
`target_item_code` varchar(140),
`target_item_name` varchar(140),
`target_asset` varchar(140),
`target_asset_name` varchar(140),
`target_qty` decimal(21,9) not null default 1.0,
`target_asset_location` varchar(140),
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`set_posting_time` int(1) not null default 0,
`finance_book` varchar(140),
`target_batch_no` varchar(140),
`target_serial_no` text,
`amended_from` varchar(140),
`target_is_fixed_asset` int(1) not null default 0,
`target_has_batch_no` int(1) not null default 0,
`target_has_serial_no` int(1) not null default 0,
`stock_items_total` decimal(21,9) not null default 0,
`asset_items_total` decimal(21,9) not null default 0,
`service_items_total` decimal(21,9) not null default 0,
`total_value` decimal(21,9) not null default 0,
`target_incoming_rate` decimal(21,9) not null default 0,
`cost_center` varchar(140),
`target_fixed_asset_account` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `posting_date`(`posting_date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-04 17:32:47,165 WARNING database DDL Query made to DB:
create table `tabAsset Depreciation Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset` varchar(140),
`naming_series` varchar(140),
`company` varchar(140),
`gross_purchase_amount` decimal(21,9) not null default 0,
`opening_accumulated_depreciation` decimal(21,9) not null default 0,
`opening_number_of_booked_depreciations` int(11) not null default 0,
`finance_book` varchar(140),
`finance_book_id` int(11) not null default 0,
`depreciation_method` varchar(140),
`total_number_of_depreciations` int(11) not null default 0,
`rate_of_depreciation` decimal(21,9) not null default 0,
`daily_prorata_based` int(1) not null default 0,
`shift_based` int(1) not null default 0,
`frequency_of_depreciation` int(11) not null default 0,
`expected_value_after_useful_life` decimal(21,9) not null default 0,
`notes` text,
`status` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
