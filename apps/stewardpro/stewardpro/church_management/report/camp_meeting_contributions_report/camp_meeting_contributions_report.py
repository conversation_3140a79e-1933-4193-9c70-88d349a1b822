# Copyright (c) 2024, StewardPro Team and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.query_builder import DocType
from frappe.query_builder.functions import <PERSON><PERSON>, <PERSON>, Avg, <PERSON>, <PERSON>, Distinct
from frappe.utils import getdate

# Define custom functions for date operations
def Year(field):
	return frappe.qb.CustomFunction("YEAR", [field])

def Month(field):
	return frappe.qb.CustomFunction("MONTH", [field])


def execute(filters=None):
	if not filters:
		filters = {}
	
	columns = get_columns()
	data = get_data(filters)
	
	return columns, data


def get_columns():
	return [
		{
			"label": _("Member"),
			"fieldname": "member",
			"fieldtype": "Link",
			"options": "Church Member",
			"width": 200
		},
		{
			"label": _("Member Name"),
			"fieldname": "member_name",
			"fieldtype": "Data",
			"width": 200
		},
		{
			"label": _("Date"),
			"fieldname": "date",
			"fieldtype": "Date",
			"width": 100
		},
		{
			"label": _("Camp Meeting Offering"),
			"fieldname": "campmeeting_offering",
			"fieldtype": "Currency",
			"width": 180
		},
		{
			"label": _("Payment Mode"),
			"fieldname": "payment_mode",
			"fieldtype": "Data",
			"width": 120
		},
		{
			"label": _("Receipt Number"),
			"fieldname": "receipt_number",
			"fieldtype": "Data",
			"width": 120
		}
	]


def get_data(filters):
	TithesOfferings = DocType("Tithes and Offerings")
	ChurchMember = DocType("Church Member")
	
	# Build main query
	query = (
		frappe.qb.from_(TithesOfferings)
		.left_join(ChurchMember)
		.on(TithesOfferings.member == ChurchMember.name)
		.select(
			TithesOfferings.member,
			ChurchMember.full_name.as_("member_name"),
			TithesOfferings.date,
			TithesOfferings.campmeeting_offering,
			TithesOfferings.payment_mode,
			TithesOfferings.receipt_number
		)
		.where(TithesOfferings.docstatus == 1)
		.where(TithesOfferings.campmeeting_offering > 0)
		.orderby(TithesOfferings.date, order=frappe.qb.desc)
	)
	
	# Apply filters
	if filters.get("year"):
		year = int(filters.get("year"))
		query = query.where(
			Year(TithesOfferings.date) == year
		)
	
	if filters.get("from_date"):
		query = query.where(TithesOfferings.date >= getdate(filters.get("from_date")))
	
	if filters.get("to_date"):
		query = query.where(TithesOfferings.date <= getdate(filters.get("to_date")))
	
	if filters.get("member"):
		query = query.where(TithesOfferings.member == filters.get("member"))
	
	data = query.run(as_dict=True)
	
	# Handle anonymous contributions
	for row in data:
		if not row.get("member"):
			row["member"] = ""
			row["member_name"] = "Anonymous"
	
	return data


def get_member_summary(filters):
	"""Get camp meeting contributions summary by member"""
	TithesOfferings = DocType("Tithes and Offerings")
	ChurchMember = DocType("Church Member")
	
	query = (
		frappe.qb.from_(TithesOfferings)
		.left_join(ChurchMember)
		.on(TithesOfferings.member == ChurchMember.name)
		.select(
			TithesOfferings.member,
			ChurchMember.full_name.as_("member_name"),
			Count(TithesOfferings.name).as_("contribution_count"),
			Sum(TithesOfferings.campmeeting_offering).as_("total_contribution"),
			Avg(TithesOfferings.campmeeting_offering).as_("avg_contribution"),
			Max(TithesOfferings.campmeeting_offering).as_("max_contribution")
		)
		.where(TithesOfferings.docstatus == 1)
		.where(TithesOfferings.campmeeting_offering > 0)
		.groupby(TithesOfferings.member, ChurchMember.full_name)
		.orderby(Sum(TithesOfferings.campmeeting_offering), order=frappe.qb.desc)
	)
	
	# Apply filters
	if filters.get("year"):
		year = int(filters.get("year"))
		query = query.where(
			Year(TithesOfferings.date) == year
		)
	
	if filters.get("from_date"):
		query = query.where(TithesOfferings.date >= getdate(filters.get("from_date")))
	
	if filters.get("to_date"):
		query = query.where(TithesOfferings.date <= getdate(filters.get("to_date")))
	
	data = query.run(as_dict=True)
	
	# Handle anonymous contributions
	for row in data:
		if not row.get("member"):
			row["member"] = ""
			row["member_name"] = "Anonymous"
	
	return data


def get_yearly_summary(filters):
	"""Get camp meeting contributions summary by year"""
	TithesOfferings = DocType("Tithes and Offerings")
	
	query = (
		frappe.qb.from_(TithesOfferings)
		.select(
			Year(TithesOfferings.date).as_("year"),
			Count(TithesOfferings.name).as_("contribution_count"),
			Sum(TithesOfferings.campmeeting_offering).as_("total_contribution"),
			Avg(TithesOfferings.campmeeting_offering).as_("avg_contribution"),
			Count(
				Distinct(TithesOfferings.member)
			).as_("unique_contributors")
		)
		.where(TithesOfferings.docstatus == 1)
		.where(TithesOfferings.campmeeting_offering > 0)
		.groupby(Year(TithesOfferings.date))
		.orderby(Year(TithesOfferings.date), order=frappe.qb.desc)
	)
	
	# Apply date filters if specified
	if filters.get("from_date"):
		query = query.where(TithesOfferings.date >= getdate(filters.get("from_date")))
	
	if filters.get("to_date"):
		query = query.where(TithesOfferings.date <= getdate(filters.get("to_date")))
	
	return query.run(as_dict=True)


def get_monthly_summary(filters):
	"""Get camp meeting contributions summary by month"""
	TithesOfferings = DocType("Tithes and Offerings")
	
	query = (
		frappe.qb.from_(TithesOfferings)
		.select(
			Year(TithesOfferings.date).as_("year"),
			Month(TithesOfferings.date).as_("month"),
			Count(TithesOfferings.name).as_("contribution_count"),
			Sum(TithesOfferings.campmeeting_offering).as_("total_contribution")
		)
		.where(TithesOfferings.docstatus == 1)
		.where(TithesOfferings.campmeeting_offering > 0)
		.groupby(
			Year(TithesOfferings.date),
			Month(TithesOfferings.date)
		)
		.orderby(
			Year(TithesOfferings.date),
			Month(TithesOfferings.date)
		)
	)
	
	# Apply filters
	if filters.get("year"):
		year = int(filters.get("year"))
		query = query.where(
			Year(TithesOfferings.date) == year
		)
	
	if filters.get("from_date"):
		query = query.where(TithesOfferings.date >= getdate(filters.get("from_date")))
	
	if filters.get("to_date"):
		query = query.where(TithesOfferings.date <= getdate(filters.get("to_date")))
	
	data = query.run(as_dict=True)
	
	# Add month names
	month_names = [
		"", "January", "February", "March", "April", "May", "June",
		"July", "August", "September", "October", "November", "December"
	]
	
	for row in data:
		month_num = row.get("month", 0)
		row["month_name"] = month_names[month_num] if month_num <= 12 else "Unknown"
	
	return data


def get_top_contributors(filters, limit=10):
	"""Get top camp meeting contributors"""
	member_summary = get_member_summary(filters)
	return member_summary[:limit]


def get_contribution_statistics(filters):
	"""Get overall contribution statistics"""
	TithesOfferings = DocType("Tithes and Offerings")
	
	query = (
		frappe.qb.from_(TithesOfferings)
		.select(
			Count(TithesOfferings.name).as_("total_contributions"),
			Sum(TithesOfferings.campmeeting_offering).as_("total_amount"),
			Avg(TithesOfferings.campmeeting_offering).as_("avg_contribution"),
			Min(TithesOfferings.campmeeting_offering).as_("min_contribution"),
			Max(TithesOfferings.campmeeting_offering).as_("max_contribution"),
			Count(
				Distinct(TithesOfferings.member)
			).as_("unique_contributors")
		)
		.where(TithesOfferings.docstatus == 1)
		.where(TithesOfferings.campmeeting_offering > 0)
	)
	
	# Apply filters
	if filters.get("year"):
		year = int(filters.get("year"))
		query = query.where(
			Year(TithesOfferings.date) == year
		)
	
	if filters.get("from_date"):
		query = query.where(TithesOfferings.date >= getdate(filters.get("from_date")))
	
	if filters.get("to_date"):
		query = query.where(TithesOfferings.date <= getdate(filters.get("to_date")))
	
	result = query.run(as_dict=True)
	return result[0] if result else {}
