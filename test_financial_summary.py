#!/usr/bin/env python3

import sys
import os

# Add the apps directory to Python path
sys.path.insert(0, '/home/<USER>/dev/velocitec/apps')

def test_financial_summary_import():
    """Test if the financial summary report can be imported"""
    
    try:
        # Try to import the module
        from stewardpro.church_management.report.financial_summary.financial_summary import execute, get_columns, get_data
        
        print("✅ Financial Summary Report: Import successful")
        
        # Test get_columns function
        columns = get_columns()
        print(f"✅ get_columns(): Returns {len(columns)} columns")
        
        # Test basic structure
        if hasattr(execute, '__call__'):
            print("✅ execute function: Available")
        else:
            print("❌ execute function: Not callable")
            
        return True
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Other Error: {e}")
        return False

if __name__ == "__main__":
    print("Testing Financial Summary Report Import...")
    print("=" * 50)
    
    success = test_financial_summary_import()
    
    print("=" * 50)
    if success:
        print("🎉 Financial Summary Report import test passed!")
        sys.exit(0)
    else:
        print("⚠️ Financial Summary Report import test failed!")
        sys.exit(1)
