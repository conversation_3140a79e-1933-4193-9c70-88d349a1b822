#!/usr/bin/env python3

import os
import sys

def check_import_issues():
    """Check for any remaining import issues"""
    
    print("Checking for remaining import issues...")
    print("=" * 50)
    
    # Check for problematic Year/Month imports
    import subprocess
    
    try:
        result = subprocess.run([
            'grep', '-r', 'from frappe.query_builder.functions import.*Year\\|Month', 
            'apps/stewardpro/stewardpro/church_management/report'
        ], capture_output=True, text=True, cwd='/home/<USER>/dev/velocitec')
        
        if result.returncode == 0 and result.stdout.strip():
            lines = result.stdout.strip().split('\n')
            # Filter out lines that are just function definitions
            import_lines = [line for line in lines if 'def Year(' not in line and 'def Month(' not in line and '.js:' not in line]
            
            if import_lines:
                print("❌ Found problematic Year/Month imports:")
                for line in import_lines:
                    print(f"  {line}")
                return False
            else:
                print("✅ No problematic Year/Month imports found")
        else:
            print("✅ No Year/Month imports found")
            
    except Exception as e:
        print(f"❌ Error checking imports: {e}")
        return False
    
    # Check for remaining frappe.qb.functions calls
    try:
        result = subprocess.run([
            'grep', '-r', 'frappe\\.qb\\.functions\\.', 
            'apps/stewardpro/stewardpro/church_management/report'
        ], capture_output=True, text=True, cwd='/home/<USER>/dev/velocitec')
        
        if result.returncode == 0 and result.stdout.strip():
            print("❌ Found remaining frappe.qb.functions calls:")
            print(result.stdout)
            return False
        else:
            print("✅ No remaining frappe.qb.functions calls found")
            
    except Exception as e:
        print(f"❌ Error checking QB functions: {e}")
        return False
    
    return True

def check_custom_functions():
    """Check that custom Year/Month functions are properly defined"""
    
    print("\nChecking custom function definitions...")
    print("=" * 50)
    
    reports_with_date_functions = [
        "apps/stewardpro/stewardpro/church_management/report/financial_summary/financial_summary.py",
        "apps/stewardpro/stewardpro/church_management/report/camp_meeting_contributions_report/camp_meeting_contributions_report.py",
        "apps/stewardpro/stewardpro/church_management/report/church_building_fund_report/church_building_fund_report.py",
        "apps/stewardpro/stewardpro/church_management/report/pending_remittance_report/pending_remittance_report.py",
        "apps/stewardpro/stewardpro/church_management/report/annual_conference_report/annual_conference_report.py"
    ]
    
    all_good = True
    
    for file_path in reports_with_date_functions:
        if os.path.exists(file_path):
            with open(file_path, 'r') as f:
                content = f.read()
            
            has_year = 'def Year(field):' in content
            has_month = 'def Month(field):' in content
            uses_year = 'Year(' in content and 'def Year(' not in content
            uses_month = 'Month(' in content and 'def Month(' not in content
            
            file_name = os.path.basename(file_path)
            
            if (uses_year and not has_year) or (uses_month and not has_month):
                print(f"❌ {file_name}: Missing custom function definitions")
                all_good = False
            elif (uses_year or uses_month) and (has_year and has_month):
                print(f"✅ {file_name}: Custom functions properly defined")
            elif not (uses_year or uses_month):
                print(f"ℹ️  {file_name}: No date functions used")
            else:
                print(f"⚠️  {file_name}: Partial date function support")
        else:
            print(f"❌ File not found: {file_path}")
            all_good = False
    
    return all_good

def main():
    """Run all verification checks"""
    
    print("Final Verification - Frappe QB Date Function Fixes")
    print("=" * 60)
    
    # Check for import issues
    imports_ok = check_import_issues()
    
    # Check custom functions
    functions_ok = check_custom_functions()
    
    print("\n" + "=" * 60)
    if imports_ok and functions_ok:
        print("🎉 All checks passed! Reports should work correctly now.")
        print("\nNext steps:")
        print("1. Test the Financial Summary report in Frappe")
        print("2. Verify other reports work as expected")
        print("3. Reports are ready for production use")
        sys.exit(0)
    else:
        print("⚠️  Some issues found. Check the output above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
