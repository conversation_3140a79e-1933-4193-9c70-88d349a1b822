#!/usr/bin/env python3

import os
import re
import sys

def fix_all_qb_issues_in_file(file_path):
    """Fix all QB issues in a Python file"""
    
    print(f"Processing: {file_path}")
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    original_content = content
    changes_made = False
    
    # Check if file uses Year or Month functions
    uses_year = 'Year(' in content
    uses_month = 'Month(' in content
    
    # Add custom functions if needed and not present
    if (uses_year or uses_month) and 'def Year(field):' not in content:
        # Find where to insert custom functions
        utils_import_pattern = r'(from frappe\.utils import [^\n]+\n)'
        match = re.search(utils_import_pattern, content)
        
        if match:
            custom_functions = '''
# Define custom functions for date operations
def Year(field):
	return frappe.qb.CustomFunction("YEAR", [field])

def Month(field):
	return frappe.qb.CustomFunction("MONTH", [field])
'''
            content = content.replace(match.group(1), match.group(1) + custom_functions)
            changes_made = True
            print(f"  - Added custom Year/Month functions")
    
    # Fix frappe.qb.functions.Year/Month calls
    if 'frappe.qb.functions.Year(' in content:
        content = re.sub(r'frappe\.qb\.functions\.Year\(', 'Year(', content)
        changes_made = True
        print(f"  - Fixed frappe.qb.functions.Year calls")
    
    if 'frappe.qb.functions.Month(' in content:
        content = re.sub(r'frappe\.qb\.functions\.Month\(', 'Month(', content)
        changes_made = True
        print(f"  - Fixed frappe.qb.functions.Month calls")
    
    # Fix frappe.qb.functions.Sum calls
    if 'frappe.qb.functions.Sum(' in content:
        # Check if Sum is imported
        if 'from frappe.query_builder.functions import' in content and 'Sum' not in content.split('from frappe.query_builder.functions import')[1].split('\n')[0]:
            # Add Sum to imports
            import_line_pattern = r'(from frappe\.query_builder\.functions import [^\n]+)'
            match = re.search(import_line_pattern, content)
            if match:
                current_imports = match.group(1)
                if not current_imports.endswith('Sum'):
                    new_imports = current_imports.rstrip() + ', Sum'
                    content = content.replace(current_imports, new_imports)
                    changes_made = True
                    print(f"  - Added Sum to imports")
        elif 'from frappe.query_builder.functions import' not in content:
            # Add the import line
            doctype_import_pattern = r'(from frappe\.query_builder import DocType\n)'
            match = re.search(doctype_import_pattern, content)
            if match:
                content = content.replace(match.group(1), match.group(1) + 'from frappe.query_builder.functions import Sum\n')
                changes_made = True
                print(f"  - Added Sum import")
        
        # Replace the function calls
        content = re.sub(r'frappe\.qb\.functions\.Sum\(', 'Sum(', content)
        changes_made = True
        print(f"  - Fixed frappe.qb.functions.Sum calls")
    
    # Fix other common QB function calls
    qb_functions = ['Count', 'Avg', 'Min', 'Max', 'Distinct']
    for func in qb_functions:
        pattern = f'frappe\\.qb\\.functions\\.{func}\\('
        if re.search(pattern, content):
            # Check if function is imported
            if 'from frappe.query_builder.functions import' in content:
                import_line_pattern = r'(from frappe\.query_builder\.functions import [^\n]+)'
                match = re.search(import_line_pattern, content)
                if match and func not in match.group(1):
                    current_imports = match.group(1)
                    new_imports = current_imports.rstrip() + f', {func}'
                    content = content.replace(current_imports, new_imports)
                    changes_made = True
                    print(f"  - Added {func} to imports")
            
            # Replace the function calls
            content = re.sub(pattern, f'{func}(', content)
            changes_made = True
            print(f"  - Fixed frappe.qb.functions.{func} calls")
    
    # Write back if changes were made
    if changes_made:
        with open(file_path, 'w') as f:
            f.write(content)
        print(f"  - File updated successfully")
        return True
    else:
        print(f"  - No changes needed")
        return False

def main():
    """Fix all QB issues in all report files"""
    
    # Base directory for reports
    base_dir = "apps/stewardpro/stewardpro/church_management/report"
    
    if not os.path.exists(base_dir):
        print(f"Error: Directory {base_dir} not found")
        sys.exit(1)
    
    # Find all Python files in report directories
    python_files = []
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            if file.endswith('.py') and not file.startswith('__'):
                python_files.append(os.path.join(root, file))
    
    print(f"Found {len(python_files)} Python files to process")
    print("=" * 50)
    
    updated_files = 0
    for file_path in python_files:
        if fix_all_qb_issues_in_file(file_path):
            updated_files += 1
        print()
    
    print("=" * 50)
    print(f"Processing complete. Updated {updated_files} files.")

if __name__ == "__main__":
    main()
