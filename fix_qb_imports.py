#!/usr/bin/env python3

import os
import re
import sys

def fix_qb_imports_in_file(file_path):
    """Fix Frappe QB import issues in a Python file"""
    
    print(f"Processing: {file_path}")
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    original_content = content
    
    # Check if file already has QB function imports
    if 'from frappe.query_builder.functions import' in content:
        print(f"  - Already has QB function imports")
    else:
        # Add QB function imports after DocType import
        doctype_import_pattern = r'from frappe\.query_builder import DocType'
        if re.search(doctype_import_pattern, content):
            # Find all QB functions used in the file
            functions_used = set()
            
            # Common QB functions to look for
            qb_functions = ['Sum', 'Count', 'Avg', 'Min', 'Max', 'Year', 'Month', 'Distinct', 'Coalesce']
            
            for func in qb_functions:
                if f'frappe.qb.functions.{func}' in content:
                    functions_used.add(func)
            
            if functions_used:
                functions_import = ', '.join(sorted(functions_used))
                import_line = f'from frappe.query_builder.functions import {functions_import}'
                
                # Insert the import after the DocType import
                content = re.sub(
                    r'(from frappe\.query_builder import DocType\n)',
                    f'\\1{import_line}\n',
                    content
                )
                print(f"  - Added imports: {functions_import}")
    
    # Replace frappe.qb.functions.FunctionName with FunctionName
    qb_function_pattern = r'frappe\.qb\.functions\.(\w+)'
    
    def replace_function(match):
        return match.group(1)
    
    content = re.sub(qb_function_pattern, replace_function, content)
    
    # Count replacements made
    replacements = len(re.findall(qb_function_pattern, original_content))
    if replacements > 0:
        print(f"  - Fixed {replacements} QB function calls")
    
    # Write back if changes were made
    if content != original_content:
        with open(file_path, 'w') as f:
            f.write(content)
        print(f"  - File updated successfully")
        return True
    else:
        print(f"  - No changes needed")
        return False

def main():
    """Fix QB imports in all report files"""
    
    # Base directory for reports
    base_dir = "apps/stewardpro/stewardpro/church_management/report"
    
    if not os.path.exists(base_dir):
        print(f"Error: Directory {base_dir} not found")
        sys.exit(1)
    
    # Find all Python files in report directories
    python_files = []
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            if file.endswith('.py') and not file.startswith('__'):
                python_files.append(os.path.join(root, file))
    
    print(f"Found {len(python_files)} Python files to process")
    print("=" * 50)
    
    updated_files = 0
    for file_path in python_files:
        if fix_qb_imports_in_file(file_path):
            updated_files += 1
        print()
    
    print("=" * 50)
    print(f"Processing complete. Updated {updated_files} files.")

if __name__ == "__main__":
    main()
