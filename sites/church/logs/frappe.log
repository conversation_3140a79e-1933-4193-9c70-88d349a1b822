2025-08-10 10:06:41,941 ERROR frappe New Exception collected in error log
Site: church
Form Dict: {'doc': '{"docstatus":0,"doctype":"Church Expense","name":"new-church-expense-dnxmtridoc","__islocal":1,"__unsaved":1,"owner":"Administrator","naming_series":"EXP-.YYYY.-","expense_date":"2025-08-10","department":"Sabbath School","expense_category":"Equipment","payment_mode":"Cash","status":"Draft","budget_reference":"Sabbath School-2025","amount":300000,"expense_description":"mamnfoknso","approved_by":"Administrator","approval_date":"2025-08-10"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-08-10 10:07:50,850 ERROR frappe New Exception collected in error log
Site: church
Form Dict: {'doc': '{"docstatus":0,"doctype":"Church Expense","name":"new-church-expense-dnxmtridoc","__islocal":1,"__unsaved":1,"owner":"Administrator","naming_series":"EXP-.YYYY.-","expense_date":"2025-08-10","department":"Sabbath School","expense_category":"Equipment","payment_mode":"Cash","status":"Draft","budget_reference":"Sabbath School-2025","amount":300000,"expense_description":"mamnfoknso","approved_by":"Administrator","approval_date":"2025-08-10"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-08-11 16:17:37,245 ERROR frappe New Exception collected in error log
Site: church
Form Dict: {'report_name': 'Financial Summary', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-08-11 16:22:44,748 ERROR frappe New Exception collected in error log
Site: church
Form Dict: {'report_name': 'Financial Summary', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-08-11 16:41:32,408 ERROR frappe New Exception collected in error log
Site: church
Form Dict: {'report_name': 'Financial Summary', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-08-11 17:56:39,348 ERROR frappe New Exception collected in error log
Site: church
Form Dict: {'report_name': 'Financial Summary', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-08-11 18:00:42,567 ERROR frappe Error while inserting deferred Error Log record: Error Log e09t29kglf: 'Title' (cannot import name 'CustomFunction' from 'frappe.query_builder.terms' (/home/<USER>/dev/velocitec/apps/frappe/frappe/query_builder/terms.py)) will get truncated, as max characters allowed is 140
Site: church
Form Dict: {}
2025-08-11 20:12:45,250 ERROR frappe New Exception collected in error log
Site: church
Form Dict: {'report_name': 'Financial Summary', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-08-11 20:15:48,955 ERROR frappe Error while inserting deferred Error Log record: Error Log t5hjrfoacl: 'Title' (cannot import name 'CustomFunction' from 'frappe.query_builder.terms' (/home/<USER>/dev/velocitec/apps/frappe/frappe/query_builder/terms.py)) will get truncated, as max characters allowed is 140
Site: church
Form Dict: {}
