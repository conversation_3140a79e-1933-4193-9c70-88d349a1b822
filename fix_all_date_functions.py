#!/usr/bin/env python3

import os
import re
import sys

def fix_date_functions_in_file(file_path):
    """Fix Year and Month function imports in a Python file"""
    
    print(f"Processing: {file_path}")
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    original_content = content
    changes_made = False
    
    # Check if file uses Year or Month functions
    uses_year = 'Year(' in content
    uses_month = 'Month(' in content
    
    if not (uses_year or uses_month):
        print(f"  - No Year/Month functions found")
        return False
    
    # Remove Year, Month from imports if present
    import_patterns = [
        r'from frappe\.query_builder\.functions import ([^,\n]*), Year, Month([^,\n]*)',
        r'from frappe\.query_builder\.functions import ([^,\n]*), Year([^,\n]*)',
        r'from frappe\.query_builder\.functions import ([^,\n]*), Month([^,\n]*)',
        r'from frappe\.query_builder\.functions import Year, Month, ([^,\n]*)',
        r'from frappe\.query_builder\.functions import Year, ([^,\n]*)',
        r'from frappe\.query_builder\.functions import Month, ([^,\n]*)',
        r'from frappe\.query_builder\.functions import Year, Month',
        r'from frappe\.query_builder\.functions import Year',
        r'from frappe\.query_builder\.functions import Month'
    ]
    
    for pattern in import_patterns:
        if re.search(pattern, content):
            if 'Year, Month' in content:
                content = re.sub(r', Year, Month', '', content)
                content = re.sub(r'Year, Month, ', '', content)
                content = re.sub(r'Year, Month', '', content)
            elif 'Year' in content:
                content = re.sub(r', Year', '', content)
                content = re.sub(r'Year, ', '', content)
                content = re.sub(r'Year', '', content)
            elif 'Month' in content:
                content = re.sub(r', Month', '', content)
                content = re.sub(r'Month, ', '', content)
                content = re.sub(r'Month', '', content)
            changes_made = True
            break
    
    # Check if file already has the custom function definitions
    if 'def Year(field):' in content and 'def Month(field):' in content:
        print(f"  - Already has custom Year/Month functions")
        if changes_made:
            with open(file_path, 'w') as f:
                f.write(content)
            print(f"  - Updated imports only")
        return changes_made
    
    # Add custom function definitions
    custom_functions = '''
# Define custom functions for date operations
def Year(field):
	from pypika import CustomFunction
	return CustomFunction("YEAR", [field])

def Month(field):
	from pypika import CustomFunction
	return CustomFunction("MONTH", [field])
'''
    
    # Find where to insert the custom functions (after imports, before first function)
    # Look for the first function definition that's not Year or Month
    first_function_pattern = r'\n\ndef (?!Year|Month)[a-zA-Z_]'
    match = re.search(first_function_pattern, content)
    
    if match:
        insert_pos = match.start() + 1  # After the newline
        content = content[:insert_pos] + custom_functions + content[insert_pos:]
        changes_made = True
    else:
        # If no function found, add at the end of imports
        imports_end_pattern = r'(from frappe\.utils import [^\n]+\n)'
        match = re.search(imports_end_pattern, content)
        if match:
            content = content.replace(match.group(1), match.group(1) + custom_functions)
            changes_made = True
    
    # Write back if changes were made
    if changes_made:
        with open(file_path, 'w') as f:
            f.write(content)
        print(f"  - Added custom Year/Month functions and fixed imports")
        return True
    else:
        print(f"  - No changes needed")
        return False

def main():
    """Fix date functions in all report files"""
    
    # Base directory for reports
    base_dir = "apps/stewardpro/stewardpro/church_management/report"
    
    if not os.path.exists(base_dir):
        print(f"Error: Directory {base_dir} not found")
        sys.exit(1)
    
    # Find all Python files in report directories that might use Year/Month
    target_files = [
        "apps/stewardpro/stewardpro/church_management/report/annual_conference_report/annual_conference_report.py",
        "apps/stewardpro/stewardpro/church_management/report/church_building_fund_report/church_building_fund_report.py",
        "apps/stewardpro/stewardpro/church_management/report/pending_remittance_report/pending_remittance_report.py"
    ]
    
    print(f"Processing {len(target_files)} specific files")
    print("=" * 50)
    
    updated_files = 0
    for file_path in target_files:
        if os.path.exists(file_path):
            if fix_date_functions_in_file(file_path):
                updated_files += 1
        else:
            print(f"File not found: {file_path}")
        print()
    
    print("=" * 50)
    print(f"Processing complete. Updated {updated_files} files.")

if __name__ == "__main__":
    main()
