#!/usr/bin/env python3

import os
import re
import sys

def fix_custom_function_in_file(file_path):
    """Fix CustomFunction usage in a Python file"""
    
    print(f"Processing: {file_path}")
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    original_content = content
    changes_made = False
    
    # Check if file has the problematic import
    if 'from frappe.query_builder.terms import CustomFunction' in content:
        # Remove the problematic import
        content = re.sub(r'from frappe\.query_builder\.terms import CustomFunction\n', '', content)
        changes_made = True
        print(f"  - Removed problematic CustomFunction import")
    
    # Check if file has the old pypika import pattern
    if 'from pypika import CustomFunction' in content:
        # Replace the function definitions
        old_year_pattern = r'def Year\(field\):\s*from pypika import CustomFunction\s*return CustomFunction\("YEAR", \[field\]\)'
        old_month_pattern = r'def Month\(field\):\s*from pypika import CustomFunction\s*return CustomFunction\("MONTH", \[field\]\)'
        
        new_year_func = 'def Year(field):\n\treturn frappe.qb.CustomFunction("YEAR", [field])'
        new_month_func = 'def Month(field):\n\treturn frappe.qb.CustomFunction("MONTH", [field])'
        
        if re.search(old_year_pattern, content, re.MULTILINE | re.DOTALL):
            content = re.sub(old_year_pattern, new_year_func, content, flags=re.MULTILINE | re.DOTALL)
            changes_made = True
            print(f"  - Fixed Year function definition")
        
        if re.search(old_month_pattern, content, re.MULTILINE | re.DOTALL):
            content = re.sub(old_month_pattern, new_month_func, content, flags=re.MULTILINE | re.DOTALL)
            changes_made = True
            print(f"  - Fixed Month function definition")
    
    # Write back if changes were made
    if changes_made:
        with open(file_path, 'w') as f:
            f.write(content)
        print(f"  - File updated successfully")
        return True
    else:
        print(f"  - No changes needed")
        return False

def main():
    """Fix CustomFunction usage in all report files"""
    
    # Base directory for reports
    base_dir = "apps/stewardpro/stewardpro/church_management/report"
    
    if not os.path.exists(base_dir):
        print(f"Error: Directory {base_dir} not found")
        sys.exit(1)
    
    # Find all Python files in report directories
    python_files = []
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            if file.endswith('.py') and not file.startswith('__'):
                python_files.append(os.path.join(root, file))
    
    print(f"Found {len(python_files)} Python files to process")
    print("=" * 50)
    
    updated_files = 0
    for file_path in python_files:
        if fix_custom_function_in_file(file_path):
            updated_files += 1
        print()
    
    print("=" * 50)
    print(f"Processing complete. Updated {updated_files} files.")

if __name__ == "__main__":
    main()
